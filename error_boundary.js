// frontend/src/components/ErrorBoundary.js
import React from ‘react’;

class ErrorBoundary extends React.Component {
constructor(props) {
super(props);
this.state = { hasError: false, error: null, errorInfo: null };
}

static getDerivedStateFromError(error) {
return { hasError: true };
}

componentDidCatch(error, errorInfo) {
this.setState({
error: error,
errorInfo: errorInfo
});

```
// Log error to monitoring service
console.error('Error Boundary caught an error:', error, errorInfo);

// In production, send to error reporting service
if (process.env.NODE_ENV === 'production') {
  // Example: Sentry.captureException(error);
}
```

}

render() {
if (this.state.hasError) {
return (
<div className="error-boundary">
<div className="error-content">
<h1>🚨 Something went wrong</h1>
<p>We’re sorry, but something unexpected happened. Please try refreshing the page.</p>

```
        <div className="error-actions">
          <button 
            className="btn-primary"
            onClick={() => window.location.reload()}
          >
            Refresh Page
          </button>
          <button 
            className="btn-secondary"
            onClick={() => this.setState({ hasError: false, error: null, errorInfo: null })}
          >
            Try Again
          </button>
        </div>
        
        {process.env.NODE_ENV === 'development' && (
          <details className="error-details">
            <summary>Error Details (Development Only)</summary>
            <pre className="error-stack">
              {this.state.error && this.state.error.toString()}
              {this.state.errorInfo.componentStack}
            </pre>
          </details>
        )}
      </div>
    </div>
  );
}

return this.props.children;
```

}
}

export default ErrorBoundary;