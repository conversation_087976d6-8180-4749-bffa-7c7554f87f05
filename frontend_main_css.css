/* frontend/src/App.css */

/* CSS Reset and Base Styles */

- {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  }

html {
scroll-behavior: smooth;
}

body {
font-family: -apple-system, BlinkMacSystemFont, ‘Segoe UI’, ‘Roboto’, ‘Oxygen’,
‘Ubuntu’, ‘Cantarell’, ‘Fira Sans’, ‘Droid Sans’, ‘Helvetica Neue’,
sans-serif;
-webkit-font-smoothing: antialiased;
-moz-osx-font-smoothing: grayscale;
background-color: #f8fafc;
color: #1e293b;
line-height: 1.6;
}

/* CSS Variables */
:root {
/* Colors */
–primary-50: #eff6ff;
–primary-100: #dbeafe;
–primary-200: #bfdbfe;
–primary-300: #93c5fd;
–primary-400: #60a5fa;
–primary-500: #3b82f6;
–primary-600: #2563eb;
–primary-700: #1d4ed8;
–primary-800: #1e40af;
–primary-900: #1e3a8a;

–gray-50: #f8fafc;
–gray-100: #f1f5f9;
–gray-200: #e2e8f0;
–gray-300: #cbd5e1;
–gray-400: #94a3b8;
–gray-500: #64748b;
–gray-600: #475569;
–gray-700: #334155;
–gray-800: #1e293b;
–gray-900: #0f172a;

–success-500: #10b981;
–warning-500: #f59e0b;
–error-500: #ef4444;

/* Spacing */
–spacing-xs: 0.25rem;
–spacing-sm: 0.5rem;
–spacing-md: 1rem;
–spacing-lg: 1.5rem;
–spacing-xl: 2rem;
–spacing-2xl: 3rem;
–spacing-3xl: 4rem;

/* Border Radius */
–radius-sm: 0.375rem;
–radius-md: 0.5rem;
–radius-lg: 0.75rem;
–radius-xl: 1rem;

/* Shadows */
–shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
–shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
–shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
–shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

/* Transitions */
–transition-fast: 150ms ease-in-out;
–transition-normal: 250ms ease-in-out;
–transition-slow: 350ms ease-in-out;
}

/* App Container */
.App {
min-height: 100vh;
display: flex;
flex-direction: column;
}

.main-content {
flex: 1;
padding-top: 4rem; /* Account for fixed navbar */
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
font-weight: 600;
line-height: 1.25;
margin-bottom: 0.5em;
}

h1 {
font-size: 2.25rem;
font-weight: 700;
}

h2 {
font-size: 1.875rem;
}

h3 {
font-size: 1.5rem;
}

h4 {
font-size: 1.25rem;
}

p {
margin-bottom: 1em;
color: var(–gray-600);
}

/* Buttons */
.btn-primary,
.btn-secondary,
.btn-outline,
.btn-danger {
display: inline-flex;
align-items: center;
gap: 0.5rem;
padding: 0.75rem 1.5rem;
border-radius: var(–radius-md);
font-weight: 500;
font-size: 0.875rem;
text-decoration: none;
border: 1px solid;
cursor: pointer;
transition: all var(–transition-fast);
white-space: nowrap;
}

.btn-primary {
background-color: var(–primary-600);
border-color: var(–primary-600);
color: white;
}

.btn-primary:hover:not(:disabled) {
background-color: var(–primary-700);
border-color: var(–primary-700);
transform: translateY(-1px);
box-shadow: var(–shadow-md);
}

.btn-secondary {
background-color: white;
border-color: var(–gray-300);
color: var(–gray-700);
}

.btn-secondary:hover:not(:disabled) {
background-color: var(–gray-50);
border-color: var(–gray-400);
}

.btn-outline {
background-color: transparent;
border-color: var(–primary-300);
color: var(–primary-600);
}

.btn-outline:hover:not(:disabled) {
background-color: var(–primary-50);
border-color: var(–primary-500);
}

.btn-danger {
background-color: var(–error-500);
border-color: var(–error-500);
color: white;
}

.btn-danger:hover:not(:disabled) {
background-color: #dc2626;
border-color: #dc2626;
}

.btn-primary:disabled,
.btn-secondary:disabled,
.btn-outline:disabled,
.btn-danger:disabled {
opacity: 0.5;
cursor: not-allowed;
transform: none;
}

.btn-primary.large,
.btn-secondary.large {
padding: 1rem 2rem;
font-size: 1rem;
}

.full-width {
width: 100%;
justify-content: center;
}

/* Form Elements */
input,
textarea,
select {
width: 100%;
padding: 0.75rem;
border: 1px solid var(–gray-300);
border-radius: var(–radius-md);
font-size: 0.875rem;
transition: border-color var(–transition-fast);
background-color: white;
}

input:focus,
textarea:focus,
select:focus {
outline: none;
border-color: var(–primary-500);
box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

input.error,
textarea.error,
select.error {
border-color: var(–error-500);
}

label {
display: block;
font-weight: 500;
margin-bottom: 0.5rem;
color: var(–gray-700);
}

.form-group {
margin-bottom: 1.5rem;
}

.error-text {
color: var(–error-500);
font-size: 0.875rem;
margin-top: 0.25rem;
display: block;
}

.error-banner {
background-color: #fef2f2;
border: 1px solid #fecaca;
color: var(–error-500);
padding: 0.75rem;
border-radius: var(–radius-md);
margin-bottom: 1rem;
font-size: 0.875rem;
}

/* Loading Spinner */
.loading-spinner {
display: inline-block;
width: 1rem;
height: 1rem;
border: 2px solid transparent;
border-top: 2px solid currentColor;
border-radius: 50%;
animation: spin 1s linear infinite;
}

.loading-spinner.small {
width: 0.875rem;
height: 0.875rem;
}

.loading-spinner.large {
width: 2rem;
height: 2rem;
}

@keyframes spin {
0% {
transform: rotate(0deg);
}
100% {
transform: rotate(360deg);
}
}

/* Cards */
.card {
background-color: white;
border: 1px solid var(–gray-200);
border-radius: var(–radius-lg);
padding: 1.5rem;
box-shadow: var(–shadow-sm);
transition: box-shadow var(–transition-fast);
}

.card:hover {
box-shadow: var(–shadow-md);
}

/* Gradient Text */
.gradient-text {
background: linear-gradient(135deg, var(–primary-600), #8b5cf6);
-webkit-background-clip: text;
-webkit-text-fill-color: transparent;
background-clip: text;
}

/* Container Utilities */
.container {
max-width: 1200px;
margin: 0 auto;
padding: 0 1rem;
}

.container-wide {
max-width: 1400px;
margin: 0 auto;
padding: 0 1rem;
}

/* Grid Utilities */
.grid {
display: grid;
}

.grid-cols-1 {
grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
grid-template-columns: repeat(4, minmax(0, 1fr));
}

.gap-4 {
gap: 1rem;
}

.gap-6 {
gap: 1.5rem;
}

.gap-8 {
gap: 2rem;
}

/* Flex Utilities */
.flex {
display: flex;
}

.flex-col {
flex-direction: column;
}

.items-center {
align-items: center;
}

.justify-center {
justify-content: center;
}

.justify-between {
justify-content: space-between;
}

/* Text Utilities */
.text-center {
text-align: center;
}

.text-sm {
font-size: 0.875rem;
}

.text-lg {
font-size: 1.125rem;
}

.text-xl {
font-size: 1.25rem;
}

.font-medium {
font-weight: 500;
}

.font-semibold {
font-weight: 600;
}

.font-bold {
font-weight: 700;
}

/* Spacing Utilities */
.mt-4 {
margin-top: 1rem;
}

.mb-4 {
margin-bottom: 1rem;
}

.py-4 {
padding-top: 1rem;
padding-bottom: 1rem;
}

.px-4 {
padding-left: 1rem;
padding-right: 1rem;
}

/* Animation Utilities */
.animate-fade-in {
animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
animation: slideUp 0.5s ease-in-out;
}

@keyframes fadeIn {
from {
opacity: 0;
}
to {
opacity: 1;
}
}

@keyframes slideUp {
from {
opacity: 0;
transform: translateY(20px);
}
to {
opacity: 1;
transform: translateY(0);
}
}

/* Responsive Design */
@media (max-width: 768px) {
.container,
.container-wide {
padding: 0 1rem;
}

h1 {
font-size: 1.875rem;
}

h2 {
font-size: 1.5rem;
}

.grid-cols-2 {
grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-3 {
grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-4 {
grid-template-columns: repeat(2, minmax(0, 1fr));
}
}

@media (max-width: 480px) {
.main-content {
padding-top: 3.5rem;
}

.btn-primary,
.btn-secondary,
.btn-outline,
.btn-danger {
padding: 0.625rem 1.25rem;
font-size: 0.875rem;
}

.grid-cols-4 {
grid-template-columns: repeat(1, minmax(0, 1fr));
}
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
*,
*::before,
*::after {
animation-duration: 0.01ms !important;
animation-iteration-count: 1 !important;
transition-duration: 0.01ms !important;
}
}

/* Focus Visible */
.btn-primary:focus-visible,
.btn-secondary:focus-visible,
.btn-outline:focus-visible,
.btn-danger:focus-visible {
outline: 2px solid var(–primary-500);
outline-offset: 2px;
}

/* Dark Mode Support (Optional) */
@media (prefers-color-scheme: dark) {
:root {
–gray-50: #0f172a;
–gray-100: #1e293b;
–gray-200: #334155;
–gray-300: #475569;
–gray-400: #64748b;
–gray-500: #94a3b8;
–gray-600: #cbd5e1;
–gray-700: #e2e8f0;
–gray-800: #f1f5f9;
–gray-900: #f8fafc;
}

body {
background-color: var(–gray-50);
color: var(–gray-800);
}
}