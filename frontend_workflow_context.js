import React, { createContext, useContext, useState, useCallback } from 'react';
import api from './frontend_api_service';
import { toast } from 'react-toastify';

const WorkflowContext = createContext();

export const useWorkflow = () => {
  const context = useContext(WorkflowContext);
  if (!context) {
    throw new Error('useWorkflow must be used within a WorkflowProvider');
  }
  return context;
};

export const WorkflowProvider = ({ children }) => {
  const [workflows, setWorkflows] = useState([]);
  const [currentWorkflow, setCurrentWorkflow] = useState(null);
  const [executions, setExecutions] = useState([]);
  const [loading, setLoading] = useState(false);

  // Fetch all workflows
  const fetchWorkflows = useCallback(async () => {
    try {
      setLoading(true);
      const response = await api.get('/workflows');
      setWorkflows(response.data);
    } catch (error) {
      toast.error('Failed to fetch workflows');
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch single workflow details
  const fetchWorkflowDetails = useCallback(async (workflowId) => {
    try {
      setLoading(true);
      const response = await api.get(`/workflows/${workflowId}`);
      setCurrentWorkflow(response.data);

      // Also fetch blocks for this workflow
      const blocksResponse = await api.get(`/workflows/${workflowId}/blocks`);
      setCurrentWorkflow(prev => ({
        ...prev,
        blocks: blocksResponse.data
      }));
      
      return response.data;
    } catch (error) {
      toast.error('Failed to fetch workflow details');
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  // Create new workflow
  const createWorkflow = useCallback(async (workflowData) => {
    try {
      const response = await api.post('/workflows', workflowData);
      setWorkflows(prev => [...prev, response.data]);
      toast.success('Workflow created successfully');
      return response.data;
    } catch (error) {
      toast.error('Failed to create workflow');
      throw error;
    }
  }, []);

  // Update workflow
  const updateWorkflow = useCallback(async (workflowId, workflowData) => {
    try {
      const response = await api.put(`/workflows/${workflowId}`, workflowData);

      // Update in workflows list
      setWorkflows(prev => 
        prev.map(w => w.id === workflowId ? response.data : w)
      );
      
      // Update current workflow if it's the one being edited
      if (currentWorkflow?.id === workflowId) {
        setCurrentWorkflow(response.data);
      }
      
      return response.data;
    } catch (error) {
      toast.error('Failed to update workflow');
      throw error;
    }
  }, [currentWorkflow]);

  // Delete workflow
  const deleteWorkflow = useCallback(async (workflowId) => {
    try {
      await api.delete(`/workflows/${workflowId}`);
      setWorkflows(prev => prev.filter(w => w.id !== workflowId));

      if (currentWorkflow?.id === workflowId) {
        setCurrentWorkflow(null);
      }
      
      toast.success('Workflow deleted successfully');
    } catch (error) {
      toast.error('Failed to delete workflow');
      throw error;
    }
  }, [currentWorkflow]);

  // Execute workflow
  const executeWorkflow = useCallback(async (workflowId) => {
    try {
      const response = await api.post(`/workflows/${workflowId}/execute`);
      toast.success('Workflow execution started');
      return response.data;
    } catch (error) {
      toast.error('Failed to execute workflow');
      throw error;
    }
  }, []);

  // Fetch workflow executions
  const fetchExecutions = useCallback(async (workflowId) => {
    try {
      const response = await api.get(`/workflows/${workflowId}/executions`);
      setExecutions(response.data);
    } catch (error) {
      toast.error('Failed to fetch executions');
    }
  }, []);

  // Create block
  const createBlock = useCallback(async (workflowId, blockData) => {
    try {
      const response = await api.post(`/workflows/${workflowId}/blocks`, blockData);

      // Update current workflow blocks
      if (currentWorkflow?.id === workflowId) {
        setCurrentWorkflow(prev => ({
          ...prev,
          blocks: [...(prev.blocks || []), response.data]
        }));
      }
      
      return response.data;
    } catch (error) {
      toast.error('Failed to create block');
      throw error;
    }
  }, [currentWorkflow]);

  // Update block
  const updateBlock = useCallback(async (workflowId, blockId, blockData) => {
    try {
      const response = await api.put(`/workflows/${workflowId}/blocks/${blockId}`, blockData);

      // Update current workflow blocks
      if (currentWorkflow?.id === workflowId) {
        setCurrentWorkflow(prev => ({
          ...prev,
          blocks: prev.blocks?.map(b => b.id === blockId ? response.data : b) || []
        }));
      }
      
      return response.data;
    } catch (error) {
      toast.error('Failed to update block');
      throw error;
    }
  }, [currentWorkflow]);

  // Delete block
  const deleteBlock = useCallback(async (workflowId, blockId) => {
    try {
      await api.delete(`/workflows/${workflowId}/blocks/${blockId}`);

      // Update current workflow blocks
      if (currentWorkflow?.id === workflowId) {
        setCurrentWorkflow(prev => ({
          ...prev,
          blocks: prev.blocks?.filter(b => b.id !== blockId) || []
        }));
      }
      
      toast.success('Block deleted successfully');
    } catch (error) {
      toast.error('Failed to delete block');
      throw error;
    }
  }, [currentWorkflow]);

  // Process NLP description
  const processNLPDescription = useCallback(async (description) => {
    try {
      setLoading(true);
      const response = await api.post('/nlp/process', { description });
      return response.data;
    } catch (error) {
      toast.error('Failed to process description');
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  // Create workflow from NLP result
  const createWorkflowFromNLP = useCallback(async (nlpResult) => {
    try {
      // Create the workflow
      const workflowData = {
        name: nlpResult.suggested_name,
        description: nlpResult.suggested_description,
        config: nlpResult.workflow_config,
        category: 'ai_generated'
      };

      const workflow = await createWorkflow(workflowData);
      
      // Create blocks for the workflow
      const createdBlocks = [];
      for (const block of nlpResult.blocks) {
        const blockData = {
          name: block.name,
          type: block.type,
          config: block.config,
          position_x: block.position?.x || 0,
          position_y: block.position?.y || 0
        };
        
        const createdBlock = await createBlock(workflow.id, blockData);
        createdBlocks.push(createdBlock);
      }
      
      // Update workflow with connections
      const updatedConfig = {
        ...workflow.config,
        connections: nlpResult.connections
      };
      
      await updateWorkflow(workflow.id, { config: updatedConfig });
      
      toast.success(`Workflow "${workflow.name}" created successfully!`);
      return workflow;
      
    } catch (error) {
      toast.error('Failed to create workflow from description');
      throw error;
    }
  }, [createWorkflow, createBlock, updateWorkflow]);

  // Get workflow statistics
  const getWorkflowStats = useCallback(() => {
    const stats = {
      total: workflows.length,
      active: workflows.filter(w => w.is_active).length,
      inactive: workflows.filter(w => !w.is_active).length,
      byCategory: workflows.reduce((acc, w) => {
        acc[w.category] = (acc[w.category] || 0) + 1;
        return acc;
      }, {}),
      recent: workflows
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, 5)
    };

    return stats;
  }, [workflows]);

  const value = {
    // State
    workflows,
    currentWorkflow,
    executions,
    loading,

    // Workflow operations
    fetchWorkflows,
    fetchWorkflowDetails,
    createWorkflow,
    updateWorkflow,
    deleteWorkflow,
    executeWorkflow,

    // Execution operations
    fetchExecutions,

    // Block operations
    createBlock,
    updateBlock,
    deleteBlock,

    // NLP operations
    processNLPDescription,
    createWorkflowFromNLP,

    // Utilities
    getWorkflowStats,
    setCurrentWorkflow
  };

  return (
    <WorkflowContext.Provider value={value}>
      {children}
    </WorkflowContext.Provider>
  );
};
