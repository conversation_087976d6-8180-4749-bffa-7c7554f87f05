"""
AutoGPT Backend - Main FastAPI Application
File: backend/main.py
"""

from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from contextlib import asynccontextmanager
import os
from datetime import datetime, timedelta
import jwt
from typing import List, Optional

# Import our modules
from backend_database import get_db, engine, Base
from backend_models import User, Workflow, Block, Execution, ApiKey
from backend_schemas import (
    UserCreate, UserLogin, UserResponse, Token,
    WorkflowCreate, WorkflowUpdate, WorkflowResponse,
    BlockCreate, BlockUpdate, BlockResponse,
    ExecutionResponse, ApiKeyCreate, ApiKeyResponse,
    NLPProcessRequest, NLPProcessResponse
)
from backend_auth import get_password_hash, verify_password, create_access_token
from backend_nlp_engine import NLPEngine
from backend_cerebras_provider import CerebrasProvider

# Environment variables
SECRET_KEY = os.getenv("SECRET_KEY", "your-super-secret-key-change-in-production")
CORS_ORIGINS = os.getenv("CORS_ORIGINS", "http://localhost:3000").split(",")

# Security
security = HTTPBearer()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    Base.metadata.create_all(bind=engine)
    print("🚀 AutoGPT Backend Started!")
    yield
    # Shutdown
    print("👋 AutoGPT Backend Shutdown")

# Initialize FastAPI
app = FastAPI(
    title="AutoGPT Backend API",
    description="Natural language to automation platform with Cerebras AI",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize components
cerebras_provider = CerebrasProvider()
nlp_engine = NLPEngine(cerebras_provider)

# Helper functions
def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security), db: Session = Depends(get_db)):
    try:
        token = credentials.credentials
        payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        user_id = payload.get("sub")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid token")
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise HTTPException(status_code=401, detail="User not found")
    return user

# Health check
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0",
        "services": {
            "database": "connected",
            "cerebras": "available" if cerebras_provider.is_available() else "unavailable"
        }
    }

# Authentication endpoints
@app.post("/auth/register", response_model=UserResponse)
async def register(user_data: UserCreate, db: Session = Depends(get_db)):
    # Check if user exists
    existing_user = db.query(User).filter(User.email == user_data.email).first()
    if existing_user:
        raise HTTPException(status_code=400, detail="Email already registered")

    # Create new user
    hashed_password = get_password_hash(user_data.password)
    user = User(
        email=user_data.email,
        name=user_data.name,
        hashed_password=hashed_password
    )
    db.add(user)
    db.commit()
    db.refresh(user)

    return UserResponse(
        id=user.id,
        email=user.email,
        name=user.name,
        created_at=user.created_at
    )

@app.post("/auth/login", response_model=Token)
async def login(user_data: UserLogin, db: Session = Depends(get_db)):
    user = db.query(User).filter(User.email == user_data.email).first()
    if not user or not verify_password(user_data.password, user.hashed_password):
        raise HTTPException(status_code=401, detail="Invalid credentials")

    access_token = create_access_token(data={"sub": str(user.id)})
    return Token(access_token=access_token, token_type="bearer")

@app.get("/auth/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    return UserResponse(
        id=current_user.id,
        email=current_user.email,
        name=current_user.name,
        created_at=current_user.created_at
    )

# NLP Processing endpoints
@app.post("/nlp/process", response_model=NLPProcessResponse)
async def process_nlp(
    request: NLPProcessRequest,
    current_user: User = Depends(get_current_user)
):
    """Process natural language description and generate workflow"""
    try:
        result = await nlp_engine.process_description(request.description)
        return NLPProcessResponse(
            success=True,
            workflow_config=result["workflow_config"],
            confidence=result["confidence"],
            blocks=result["blocks"],
            connections=result["connections"],
            suggested_name=result["suggested_name"],
            suggested_description=result["suggested_description"]
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"NLP processing failed: {str(e)}")

# Workflow endpoints
@app.get("/workflows", response_model=List[WorkflowResponse])
async def get_workflows(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    workflows = db.query(Workflow).filter(Workflow.user_id == current_user.id).all()
    return [WorkflowResponse.from_orm(w) for w in workflows]

@app.post("/workflows", response_model=WorkflowResponse)
async def create_workflow(
    workflow_data: WorkflowCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    workflow = Workflow(
        name=workflow_data.name,
        description=workflow_data.description,
        config=workflow_data.config,
        user_id=current_user.id
    )
    db.add(workflow)
    db.commit()
    db.refresh(workflow)
    return WorkflowResponse.from_orm(workflow)

@app.get("/workflows/{workflow_id}", response_model=WorkflowResponse)
async def get_workflow(
    workflow_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    workflow = db.query(Workflow).filter(
        Workflow.id == workflow_id,
        Workflow.user_id == current_user.id
    ).first()

    if not workflow:
        raise HTTPException(status_code=404, detail="Workflow not found")

    return WorkflowResponse.from_orm(workflow)

@app.put("/workflows/{workflow_id}", response_model=WorkflowResponse)
async def update_workflow(
    workflow_id: int,
    workflow_data: WorkflowUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    workflow = db.query(Workflow).filter(
        Workflow.id == workflow_id,
        Workflow.user_id == current_user.id
    ).first()

    if not workflow:
        raise HTTPException(status_code=404, detail="Workflow not found")

    for field, value in workflow_data.dict(exclude_unset=True).items():
        setattr(workflow, field, value)

    workflow.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(workflow)

    return WorkflowResponse.from_orm(workflow)

@app.delete("/workflows/{workflow_id}")
async def delete_workflow(
    workflow_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    workflow = db.query(Workflow).filter(
        Workflow.id == workflow_id,
        Workflow.user_id == current_user.id
    ).first()

    if not workflow:
        raise HTTPException(status_code=404, detail="Workflow not found")

    db.delete(workflow)
    db.commit()

    return {"message": "Workflow deleted successfully"}

@app.post("/workflows/{workflow_id}/execute")
async def execute_workflow(
    workflow_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    workflow = db.query(Workflow).filter(
        Workflow.id == workflow_id,
        Workflow.user_id == current_user.id
    ).first()

    if not workflow:
        raise HTTPException(status_code=404, detail="Workflow not found")

    # Create execution record
    execution = Execution(
        workflow_id=workflow_id,
        status="running",
        started_at=datetime.utcnow()
    )
    db.add(execution)
    db.commit()
    db.refresh(execution)

    try:
        # Mock execution for now - would use actual workflow engine
        result = {
            "status": "completed",
            "execution_time": 2.5,
            "output": "Workflow executed successfully"
        }
        
        # Update execution record
        execution.status = result["status"]
        execution.completed_at = datetime.utcnow()
        execution.result = result
        execution.execution_time = result.get("execution_time", 0)
        
        db.commit()
        
        return {"execution_id": execution.id, "status": "completed"}
        
    except Exception as e:
        execution.status = "failed"
        execution.completed_at = datetime.utcnow()
        execution.error = str(e)
        db.commit()
        
        raise HTTPException(status_code=500, detail=f"Workflow execution failed: {str(e)}")

# Block endpoints
@app.get("/workflows/{workflow_id}/blocks", response_model=List[BlockResponse])
async def get_workflow_blocks(
    workflow_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify workflow ownership
    workflow = db.query(Workflow).filter(
        Workflow.id == workflow_id,
        Workflow.user_id == current_user.id
    ).first()

    if not workflow:
        raise HTTPException(status_code=404, detail="Workflow not found")

    blocks = db.query(Block).filter(Block.workflow_id == workflow_id).all()
    return [BlockResponse.from_orm(b) for b in blocks]

@app.post("/workflows/{workflow_id}/blocks", response_model=BlockResponse)
async def create_block(
    workflow_id: int,
    block_data: BlockCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify workflow ownership
    workflow = db.query(Workflow).filter(
        Workflow.id == workflow_id,
        Workflow.user_id == current_user.id
    ).first()

    if not workflow:
        raise HTTPException(status_code=404, detail="Workflow not found")

    block = Block(
        workflow_id=workflow_id,
        name=block_data.name,
        type=block_data.type,
        config=block_data.config,
        position_x=block_data.position_x,
        position_y=block_data.position_y
    )
    db.add(block)
    db.commit()
    db.refresh(block)

    return BlockResponse.from_orm(block)

# Execution endpoints
@app.get("/workflows/{workflow_id}/executions", response_model=List[ExecutionResponse])
async def get_workflow_executions(
    workflow_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify workflow ownership
    workflow = db.query(Workflow).filter(
        Workflow.id == workflow_id,
        Workflow.user_id == current_user.id
    ).first()

    if not workflow:
        raise HTTPException(status_code=404, detail="Workflow not found")

    executions = db.query(Execution).filter(
        Execution.workflow_id == workflow_id
    ).order_by(Execution.started_at.desc()).limit(50).all()

    return [ExecutionResponse.from_orm(e) for e in executions]

# API Key endpoints
@app.get("/settings/api-keys", response_model=List[ApiKeyResponse])
async def get_api_keys(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    api_keys = db.query(ApiKey).filter(ApiKey.user_id == current_user.id).all()
    return [ApiKeyResponse.from_orm(key) for key in api_keys]

@app.post("/settings/api-keys", response_model=ApiKeyResponse)
async def create_api_key(
    api_key_data: ApiKeyCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    api_key = ApiKey(
        user_id=current_user.id,
        name=api_key_data.name,
        provider=api_key_data.provider,
        api_key=api_key_data.api_key,  # In production, encrypt this
        is_active=True
    )
    db.add(api_key)
    db.commit()
    db.refresh(api_key)

    return ApiKeyResponse.from_orm(api_key)

# AI Model endpoints
@app.get("/ai/models")
async def get_available_models():
    """Get available AI models from Cerebras"""
    return await cerebras_provider.get_available_models()

@app.post("/ai/test")
async def test_ai_model(
    request: dict,
    current_user: User = Depends(get_current_user)
):
    """Test AI model with a prompt"""
    try:
        response = await cerebras_provider.generate_chat_response(
            messages=[{"role": "user", "content": request.get("prompt", "")}],
            model=request.get("model", "llama-4-scout-17b-16e-instruct"),
            max_tokens=request.get("max_tokens", 200),
            temperature=request.get("temperature", 0.7)
        )
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI test failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
