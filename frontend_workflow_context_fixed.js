import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './frontend_auth_context_fixed';

const WorkflowContext = createContext();

export const useWorkflow = () => {
  const context = useContext(WorkflowContext);
  if (!context) {
    throw new Error('useWorkflow must be used within a WorkflowProvider');
  }
  return context;
};

export const WorkflowProvider = ({ children }) => {
  const { token } = useAuth();
  const [workflows, setWorkflows] = useState([]);
  const [currentWorkflow, setCurrentWorkflow] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [blocks, setBlocks] = useState([]);
  const [connections, setConnections] = useState([]);

  const apiCall = async (url, options = {}) => {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`API call failed: ${response.status}`);
    }

    return response.json();
  };

  const fetchWorkflows = async () => {
    if (!token) return;
    
    setIsLoading(true);
    try {
      const data = await apiCall('/api/workflows');
      setWorkflows(data);
    } catch (error) {
      console.error('Failed to fetch workflows:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const createWorkflow = async (workflowData) => {
    try {
      const newWorkflow = await apiCall('/api/workflows', {
        method: 'POST',
        body: JSON.stringify(workflowData),
      });
      
      setWorkflows(prev => [...prev, newWorkflow]);
      return { success: true, workflow: newWorkflow };
    } catch (error) {
      console.error('Failed to create workflow:', error);
      return { success: false, error: error.message };
    }
  };

  const updateWorkflow = async (id, workflowData) => {
    try {
      const updatedWorkflow = await apiCall(`/api/workflows/${id}`, {
        method: 'PUT',
        body: JSON.stringify(workflowData),
      });
      
      setWorkflows(prev => 
        prev.map(workflow => 
          workflow.id === id ? updatedWorkflow : workflow
        )
      );
      
      if (currentWorkflow && currentWorkflow.id === id) {
        setCurrentWorkflow(updatedWorkflow);
      }
      
      return { success: true, workflow: updatedWorkflow };
    } catch (error) {
      console.error('Failed to update workflow:', error);
      return { success: false, error: error.message };
    }
  };

  const deleteWorkflow = async (id) => {
    try {
      await apiCall(`/api/workflows/${id}`, {
        method: 'DELETE',
      });
      
      setWorkflows(prev => prev.filter(workflow => workflow.id !== id));
      
      if (currentWorkflow && currentWorkflow.id === id) {
        setCurrentWorkflow(null);
      }
      
      return { success: true };
    } catch (error) {
      console.error('Failed to delete workflow:', error);
      return { success: false, error: error.message };
    }
  };

  const executeWorkflow = async (id) => {
    try {
      const result = await apiCall(`/api/workflows/${id}/execute`, {
        method: 'POST',
      });
      
      return { success: true, execution: result };
    } catch (error) {
      console.error('Failed to execute workflow:', error);
      return { success: false, error: error.message };
    }
  };

  const processNLP = async (description) => {
    try {
      const result = await apiCall('/api/nlp/process', {
        method: 'POST',
        body: JSON.stringify({ description }),
      });
      
      return { success: true, result };
    } catch (error) {
      console.error('Failed to process NLP:', error);
      return { success: false, error: error.message };
    }
  };

  const fetchWorkflowBlocks = async (workflowId) => {
    try {
      const blocks = await apiCall(`/api/workflows/${workflowId}/blocks`);
      setBlocks(blocks);
      return { success: true, blocks };
    } catch (error) {
      console.error('Failed to fetch workflow blocks:', error);
      return { success: false, error: error.message };
    }
  };

  const createBlock = async (workflowId, blockData) => {
    try {
      const newBlock = await apiCall(`/api/workflows/${workflowId}/blocks`, {
        method: 'POST',
        body: JSON.stringify(blockData),
      });
      
      setBlocks(prev => [...prev, newBlock]);
      return { success: true, block: newBlock };
    } catch (error) {
      console.error('Failed to create block:', error);
      return { success: false, error: error.message };
    }
  };

  const updateBlocks = (newBlocks) => {
    setBlocks(newBlocks);
  };

  const updateConnections = (newConnections) => {
    setConnections(newConnections);
  };

  useEffect(() => {
    if (token) {
      fetchWorkflows();
    }
  }, [token]);

  const value = {
    workflows,
    currentWorkflow,
    setCurrentWorkflow,
    isLoading,
    blocks,
    connections,
    fetchWorkflows,
    createWorkflow,
    updateWorkflow,
    deleteWorkflow,
    executeWorkflow,
    processNLP,
    fetchWorkflowBlocks,
    createBlock,
    updateBlocks,
    updateConnections,
  };

  return (
    <WorkflowContext.Provider value={value}>
      {children}
    </WorkflowContext.Provider>
  );
};

export default WorkflowContext;
