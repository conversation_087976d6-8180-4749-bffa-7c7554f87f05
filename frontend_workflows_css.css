/* frontend/src/pages/Workflows.css */

.workflows-page {
padding: 2rem 0;
background-color: var(–gray-50);
min-height: calc(100vh - 4rem);
}

.workflows-container {
max-width: 1400px;
margin: 0 auto;
padding: 0 1rem;
}

/* Workflows Header */
.workflows-header {
display: flex;
justify-content: space-between;
align-items: flex-start;
margin-bottom: 2rem;
padding: 2rem;
background: linear-gradient(135deg, var(–gray-900) 0%, var(–gray-800) 100%);
border-radius: var(–radius-xl);
color: white;
position: relative;
overflow: hidden;
}

.workflows-header::before {
content: ‘’;
position: absolute;
top: 0;
left: 0;
right: 0;
bottom: 0;
background: url(‘data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="workflow-grid" width="25" height="25" patternUnits="userSpaceOnUse"><path d="M 25 0 L 0 0 0 25" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23workflow-grid)"/></svg>’);
z-index: 0;
}

.header-content {
position: relative;
z-index: 1;
}

.header-actions {
display: flex;
gap: 1rem;
position: relative;
z-index: 1;
}

.workflows-header h1 {
font-size: 2.5rem;
font-weight: 700;
margin-bottom: 0.5rem;
text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.workflows-header p {
font-size: 1.125rem;
color: rgba(255, 255, 255, 0.9);
margin: 0;
}

/* Filters and Search */
.workflows-filters {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 2rem;
padding: 1.5rem;
background: white;
border-radius: var(–radius-lg);
box-shadow: var(–shadow-sm);
border: 1px solid var(–gray-200);
gap: 1rem;
flex-wrap: wrap;
}

.filters-left {
display: flex;
gap: 1rem;
align-items: center;
flex-wrap: wrap;
}

.filters-right {
display: flex;
gap: 1rem;
align-items: center;
}

.search-box {
position: relative;
min-width: 300px;
}

.search-input {
width: 100%;
padding: 0.75rem 1rem 0.75rem 2.5rem;
border: 1px solid var(–gray-300);
border-radius: var(–radius-md);
font-size: 0.875rem;
transition: all var(–transition-fast);
}

.search-input:focus {
border-color: var(–primary-500);
box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
outline: none;
}

.search-icon {
position: absolute;
left: 0.75rem;
top: 50%;
transform: translateY(-50%);
color: var(–gray-400);
font-size: 1rem;
}

.filter-select,
.sort-select {
padding: 0.75rem 1rem;
border: 1px solid var(–gray-300);
border-radius: var(–radius-md);
font-size: 0.875rem;
background: white;
cursor: pointer;
transition: border-color var(–transition-fast);
}

.filter-select:focus,
.sort-select:focus {
border-color: var(–primary-500);
outline: none;
box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.view-toggle {
display: flex;
background: var(–gray-100);
border-radius: var(–radius-md);
overflow: hidden;
}

.view-btn {
padding: 0.5rem 0.75rem;
border: none;
background: transparent;
color: var(–gray-600);
cursor: pointer;
font-size: 1rem;
transition: all var(–transition-fast);
}

.view-btn:hover {
color: var(–gray-900);
}

.view-btn.active {
background: white;
color: var(–primary-600);
box-shadow: var(–shadow-sm);
}

/* Bulk Actions */
.bulk-actions {
display: flex;
justify-content: space-between;
align-items: center;
padding: 1rem 1.5rem;
background: var(–primary-50);
border: 1px solid var(–primary-200);
border-radius: var(–radius-lg);
margin-bottom: 1.5rem;
}

.bulk-info {
font-size: 0.875rem;
color: var(–primary-700);
font-weight: 500;
}

.bulk-buttons {
display: flex;
gap: 1rem;
}

/* Workflows Content */
.workflows-content {
margin-bottom: 3rem;
}

.workflows-content.grid {
display: grid;
grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
gap: 1.5rem;
}

.workflows-content.list .workflow-item {
display: flex;
align-items: center;
gap: 1rem;
padding: 1.5rem;
background: white;
border: 1px solid var(–gray-200);
border-radius: var(–radius-lg);
margin-bottom: 1rem;
transition: all var(–transition-fast);
}

.workflows-content.list .workflow-item:hover {
transform: translateY(-1px);
box-shadow: var(–shadow-md);
border-color: var(–primary-300);
}

.workflows-content.grid .workflow-item {
background: white;
border: 1px solid var(–gray-200);
border-radius: var(–radius-lg);
padding: 1.5rem;
transition: all var(–transition-fast);
display: flex;
flex-direction: column;
}

.workflows-content.grid .workflow-item:hover {
transform: translateY(-4px);
box-shadow: var(–shadow-lg);
border-color: var(–primary-300);
}

/* Workflow Item Components */
.workflow-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 1rem;
}

.workflow-select {
display: flex;
align-items: center;
}

.workflow-select input[type=“checkbox”] {
width: 1rem;
height: 1rem;
margin: 0;
}

.workflow-icon {
width: 3rem;
height: 3rem;
border-radius: var(–radius-lg);
background: var(–primary-50);
display: flex;
align-items: center;
justify-content: center;
font-size: 1.5rem;
flex-shrink: 0;
}

.workflow-status {
display: flex;
align-items: center;
}

.status-badge {
padding: 0.25rem 0.75rem;
border-radius: 1rem;
font-size: 0.75rem;
font-weight: 600;
text-transform: uppercase;
letter-spacing: 0.025em;
}

.status-badge.active {
background: var(–success-500);
color: white;
}

.status-badge.inactive {
background: var(–gray-300);
color: var(–gray-700);
}

.workflow-content {
flex: 1;
margin-bottom: 1rem;
}

.workflow-title {
font-size: 1.125rem;
font-weight: 600;
color: var(–gray-900);
text-decoration: none;
margin-bottom: 0.5rem;
display: block;
transition: color var(–transition-fast);
}

.workflow-title:hover {
color: var(–primary-600);
text-decoration: none;
}

.workflow-description {
color: var(–gray-600);
font-size: 0.875rem;
line-height: 1.5;
margin-bottom: 1rem;
display: -webkit-box;
-webkit-line-clamp: 2;
-webkit-box-orient: vertical;
overflow: hidden;
}

.workflow-meta {
display: flex;
gap: 1rem;
font-size: 0.75rem;
color: var(–gray-500);
flex-wrap: wrap;
}

.meta-item {
display: flex;
align-items: center;
gap: 0.25rem;
}

.meta-icon {
font-size: 0.875rem;
}

.workflow-actions {
display: flex;
gap: 0.5rem;
justify-content: flex-end;
margin-top: auto;
}

.action-btn {
width: 2rem;
height: 2rem;
border: 1px solid var(–gray-300);
background: white;
border-radius: var(–radius-md);
display: flex;
align-items: center;
justify-content: center;
font-size: 0.875rem;
cursor: pointer;
text-decoration: none;
color: var(–gray-600);
transition: all var(–transition-fast);
}

.action-btn:hover {
background: var(–gray-50);
color: var(–gray-900);
transform: translateY(-1px);
text-decoration: none;
}

.action-btn:disabled {
opacity: 0.5;
cursor: not-allowed;
transform: none;
}

.action-btn.delete:hover {
background: #fef2f2;
color: var(–error-500);
border-color: #fecaca;
}

/* Empty States */
.empty-workflows {
text-align: center;
padding: 4rem 2rem;
background: white;
border: 1px solid var(–gray-200);
border-radius: var(–radius-xl);
margin: 2rem 0;
}

.empty-icon {
font-size: 4rem;
margin-bottom: 1.5rem;
opacity: 0.6;
}

.empty-workflows h3 {
font-size: 1.5rem;
font-weight: 600;
color: var(–gray-900);
margin-bottom: 0.75rem;
}

.empty-workflows p {
color: var(–gray-600);
margin-bottom: 2rem;
font-size: 1rem;
}

.empty-actions {
display: flex;
gap: 1rem;
justify-content: center;
flex-wrap: wrap;
}

/* Workflow Stats */
.workflows-stats {
background: white;
border: 1px solid var(–gray-200);
border-radius: var(–radius-lg);
padding: 1.5rem;
margin-bottom: 2rem;
box-shadow: var(–shadow-sm);
}

.stats-header {
margin-bottom: 1.5rem;
padding-bottom: 1rem;
border-bottom: 1px solid var(–gray-200);
}

.stats-header h3 {
font-size: 1.125rem;
font-weight: 600;
color: var(–gray-900);
margin: 0;
}

.stats-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
gap: 1.5rem;
}

.stat-item {
text-align: center;
}

.stat-number {
font-size: 2rem;
font-weight: 700;
color: var(–primary-600);
margin-bottom: 0.25rem;
}

.stat-label {
font-size: 0.875rem;
color: var(–gray-600);
font-weight: 500;
}

/* Quick Tips */
.workflows-tips {
background: linear-gradient(135deg, var(–primary-50) 0%, var(–gray-50) 100%);
border-radius: var(–radius-lg);
padding: 1.5rem;
border: 1px solid var(–primary-200);
}

.tips-header {
margin-bottom: 1rem;
}

.tips-header h3 {
font-size: 1.125rem;
font-weight: 600;
color: var(–gray-900);
margin: 0;
}

.tips-list {
display: grid;
gap: 0.75rem;
}

.tip-item {
display: flex;
align-items: center;
gap: 0.75rem;
font-size: 0.875rem;
color: var(–gray-700);
background: white;
padding: 1rem;
border-radius: var(–radius-md);
border: 1px solid var(–gray-200);
}

.tip-icon {
font-size: 1.125rem;
flex-shrink: 0;
}

/* List View Specific Styles */
.workflows-content.list .workflow-header {
margin-bottom: 0;
}

.workflows-content.list .workflow-content {
margin-bottom: 0;
display: flex;
flex-direction: column;
justify-content: center;
}

.workflows-content.list .workflow-meta {
margin-top: 0.5rem;
}

.workflows-content.list .workflow-actions {
margin-top: 0;
flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
.workflows-page {
padding: 1rem 0;
}

.workflows-container {
padding: 0 1rem;
}

.workflows-header {
flex-direction: column;
gap: 1rem;
padding: 1.5rem;
text-align: center;
}

.workflows-header h1 {
font-size: 2rem;
}

.workflows-filters {
flex-direction: column;
align-items: stretch;
gap: 1rem;
}

.filters-left,
.filters-right {
flex-direction: column;
gap: 1rem;
}

.search-box {
min-width: auto;
}

.workflows-content.grid {
grid-template-columns: 1fr;
}

.workflows-content.list .workflow-item {
flex-direction: column;
align-items: flex-start;
gap: 1rem;
}

.workflow-header {
width: 100%;
justify-content: space-between;
}

.workflow-actions {
justify-content: flex-start;
margin-top: 1rem;
}

.empty-actions {
flex-direction: column;
align-items: center;
}

.stats-grid {
grid-template-columns: repeat(2, 1fr);
}

.bulk-actions {
flex-direction: column;
gap: 1rem;
text-align: center;
}
}

@media (max-width: 480px) {
.workflows-header {
padding: 1rem;
}

.workflows-filters {
padding: 1rem;
}

.workflow-item {
padding: 1rem !important;
}

.empty-workflows {
padding: 2rem 1rem;
}

.workflow-meta {
flex-direction: column;
gap: 0.5rem;
align-items: flex-start;
}

.stats-grid {
grid-template-columns: 1fr;
}

.tips-list {
gap: 0.5rem;
}

.tip-item {
flex-direction: column;
text-align: center;
gap: 0.5rem;
}
}

/* Focus and Accessibility */
.workflow-item:focus-within {
outline: 2px solid var(–primary-500);
outline-offset: 2px;
}

.action-btn:focus-visible {
outline: 2px solid var(–primary-500);
outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
.workflow-item {
border-width: 2px;
}

.status-badge.active {
border: 2px solid var(–success-700);
}

.status-badge.inactive {
border: 2px solid var(–gray-500);
}
}

/* Print Styles */
@media print {
.workflows-header {
background: none !important;
color: var(–gray-900) !important;
}

.workflows-filters,
.workflow-actions,
.header-actions {
display: none;
}

.workflow-item {
break-inside: avoid;
box-shadow: none !important;
border: 1px solid var(–gray-300) !important;
}
}