import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default api;

// Specific API functions for different endpoints

export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  getCurrentUser: () => api.get('/auth/me'),
};

export const workflowAPI = {
  getAll: () => api.get('/workflows'),
  getById: (id) => api.get(`/workflows/${id}`),
  create: (data) => api.post('/workflows', data),
  update: (id, data) => api.put(`/workflows/${id}`, data),
  delete: (id) => api.delete(`/workflows/${id}`),
  execute: (id) => api.post(`/workflows/${id}/execute`),
  getExecutions: (id) => api.get(`/workflows/${id}/executions`),
};

export const blockAPI = {
  getByWorkflow: (workflowId) => api.get(`/workflows/${workflowId}/blocks`),
  create: (workflowId, data) => api.post(`/workflows/${workflowId}/blocks`, data),
  update: (workflowId, blockId, data) => api.put(`/workflows/${workflowId}/blocks/${blockId}`, data),
  delete: (workflowId, blockId) => api.delete(`/workflows/${workflowId}/blocks/${blockId}`),
};

export const nlpAPI = {
  processDescription: (description) => api.post('/nlp/process', { description }),
};

export const settingsAPI = {
  getApiKeys: () => api.get('/settings/api-keys'),
  createApiKey: (data) => api.post('/settings/api-keys', data),
  deleteApiKey: (id) => api.delete(`/settings/api-keys/${id}`),
  updateProfile: (data) => api.put('/settings/profile', data),
};

export const aiAPI = {
  getModels: () => api.get('/ai/models'),
  testModel: (data) => api.post('/ai/test', data),
};

export const healthAPI = {
  check: () => api.get('/health'),
};
