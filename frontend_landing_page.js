// frontend/src/pages/Landing.js
import React from ‘react’;
import { Link } from ‘react-router-dom’;
import { useAuth } from ‘../contexts/AuthContext’;
import ‘./Landing.css’;

const Landing = () => {
const { isAuthenticated } = useAuth();

return (
<div className="landing">
{/* Hero Section */}
<section className="hero">
<div className="hero-container">
<div className="hero-content">
<div className="hero-badge">
<span className="badge-icon">⚡</span>
<span>Powered by Cerebras • 20x Faster AI</span>
</div>

```
        <h1 className="hero-title">
          Transform Ideas into 
          <span className="gradient-text"> AI Automations</span>
        </h1>
        
        <p className="hero-subtitle">
          Describe your automation in plain English and watch AutoGPT build it instantly. 
          Ultra-fast Cerebras AI delivers 2,600+ tokens/second for real-time workflow generation.
        </p>
        
        <div className="hero-actions">
          {isAuthenticated ? (
            <Link to="/dashboard" className="btn-primary">
              <span className="btn-icon">🚀</span>
              Go to Dashboard
            </Link>
          ) : (
            <>
              <Link to="/register" className="btn-primary">
                <span className="btn-icon">🚀</span>
                Start Building Free
              </Link>
              <Link to="/login" className="btn-secondary">
                Sign In
              </Link>
            </>
          )}
        </div>
        
        <div className="hero-stats">
          <div className="stat">
            <div className="stat-number">2,600+</div>
            <div className="stat-label">Tokens/Second</div>
          </div>
          <div className="stat">
            <div className="stat-number">20x</div>
            <div className="stat-label">Faster than GPU</div>
          </div>
          <div className="stat">
            <div className="stat-number">94%</div>
            <div className="stat-label">NLP Accuracy</div>
          </div>
        </div>
      </div>
      
      <div className="hero-visual">
        <div className="demo-card">
          <div className="demo-header">
            <div className="demo-title">NLP to Automation</div>
            <div className="demo-status">Live Demo</div>
          </div>
          <div className="demo-input">
            <div className="input-label">Describe your automation:</div>
            <div className="input-text">
              "Create an agent that monitors my Gmail, reads customer support emails, 
              generates helpful responses using AI, and sends them back automatically"
            </div>
          </div>
          <div className="demo-arrow">↓</div>
          <div className="demo-output">
            <div className="workflow-blocks">
              <div className="block">📧 Email Reader</div>
              <div className="connector">→</div>
              <div className="block">🤖 AI Generator</div>
              <div className="connector">→</div>
              <div className="block">📤 Email Sender</div>
            </div>
            <div className="confidence">Confidence: 96.8%</div>
          </div>
        </div>
      </div>
    </div>
  </section>

  {/* Features Section */}
  <section className="features">
    <div className="features-container">
      <div className="section-header">
        <h2>Why Choose AutoGPT?</h2>
        <p>The most advanced natural language to automation platform</p>
      </div>
      
      <div className="features-grid">
        <div className="feature-card">
          <div className="feature-icon">🧠</div>
          <h3>Natural Language Processing</h3>
          <p>Describe automations in plain English. Our advanced NLP engine understands intent and builds workflows with 94% accuracy.</p>
        </div>
        
        <div className="feature-card">
          <div className="feature-icon">⚡</div>
          <h3>Ultra-Fast Cerebras AI</h3>
          <p>20x faster inference with 2,600+ tokens/second. Real-time automation generation without waiting.</p>
        </div>
        
        <div className="feature-card">
          <div className="feature-icon">🔧</div>
          <h3>Visual Workflow Builder</h3>
          <p>Drag-and-drop blocks to create complex automations. Connect email, AI, web scraping, and social media blocks.</p>
        </div>
        
        <div className="feature-card">
          <div className="feature-icon">📊</div>
          <h3>Real-Time Monitoring</h3>
          <p>Watch your automations run live with detailed execution logs, performance metrics, and cost tracking.</p>
        </div>
        
        <div className="feature-card">
          <div className="feature-icon">🔗</div>
          <h3>Extensive Integrations</h3>
          <p>Connect to Gmail, social media platforms, webhooks, APIs, and file systems with pre-built blocks.</p>
        </div>
        
        <div className="feature-card">
          <div className="feature-icon">🚀</div>
          <h3>Production Ready</h3>
          <p>Enterprise-grade security, error handling, and scalability. Deploy with confidence using Docker and cloud platforms.</p>
        </div>
      </div>
    </div>
  </section>

  {/* Use Cases Section */}
  <section className="use-cases">
    <div className="use-cases-container">
      <div className="section-header">
        <h2>Popular Automation Ideas</h2>
        <p>See what others are building with AutoGPT</p>
      </div>
      
      <div className="use-cases-grid">
        <div className="use-case">
          <div className="use-case-icon">📧</div>
          <h3>Customer Support Automation</h3>
          <p>"Monitor Gmail for support emails, generate AI responses, and send them automatically"</p>
          <div className="use-case-blocks">
            <span className="block-tag">Email Reader</span>
            <span className="block-tag">AI Generator</span>
            <span className="block-tag">Email Sender</span>
          </div>
        </div>
        
        <div className="use-case">
          <div className="use-case-icon">📱</div>
          <h3>Social Media Manager</h3>
          <p>"Track trending topics, create engaging posts, and schedule them across platforms"</p>
          <div className="use-case-blocks">
            <span className="block-tag">Web Scraper</span>
            <span className="block-tag">AI Content</span>
            <span className="block-tag">Social Poster</span>
          </div>
        </div>
        
        <div className="use-case">
          <div className="use-case-icon">📊</div>
          <h3>Daily Report Generator</h3>
          <p>"Analyze sales data every morning and email insights to the team"</p>
          <div className="use-case-blocks">
            <span className="block-tag">Scheduler</span>
            <span className="block-tag">File Reader</span>
            <span className="block-tag">AI Analysis</span>
          </div>
        </div>
        
        <div className="use-case">
          <div className="use-case-icon">🔔</div>
          <h3>Smart Monitoring</h3>
          <p>"Watch competitor websites and alert when prices change"</p>
          <div className="use-case-blocks">
            <span className="block-tag">Web Monitor</span>
            <span className="block-tag">Conditional</span>
            <span className="block-tag">Notification</span>
          </div>
        </div>
      </div>
    </div>
  </section>

  {/* Performance Section */}
  <section className="performance">
    <div className="performance-container">
      <div className="performance-content">
        <h2>Cerebras Performance Advantage</h2>
        <p>See how our ultra-fast AI infrastructure compares to traditional solutions</p>
        
        <div className="comparison-table">
          <div className="comparison-header">
            <div></div>
            <div>Traditional GPU</div>
            <div>AutoGPT + Cerebras</div>
          </div>
          
          <div className="comparison-row">
            <div className="metric">Tokens per Second</div>
            <div className="value">~130</div>
            <div className="value highlight">2,600+</div>
          </div>
          
          <div className="comparison-row">
            <div className="metric">Response Time</div>
            <div className="value">10-30s</div>
            <div className="value highlight">&lt;2s</div>
          </div>
          
          <div className="comparison-row">
            <div className="metric">Cost per 1K Tokens</div>
            <div className="value">$0.002</div>
            <div className="value highlight">$0.0004</div>
          </div>
          
          <div className="comparison-row">
            <div className="metric">Context Window</div>
            <div className="value">4K-32K</div>
            <div className="value highlight">128K</div>
          </div>
        </div>
      </div>
    </div>
  </section>

  {/* CTA Section */}
  <section className="cta">
    <div className="cta-container">
      <div className="cta-content">
        <h2>Ready to Automate Your Workflows?</h2>
        <p>Join thousands of users building AI automations in seconds, not hours.</p>
        
        {!isAuthenticated && (
          <div className="cta-actions">
            <Link to="/register" className="btn-primary large">
              <span className="btn-icon">🚀</span>
              Start Building Free
            </Link>
            <div className="cta-note">
              No credit card required • Free trial included
            </div>
          </div>
        )}
      </div>
    </div>
  </section>
</div>
```

);
};

export default Landing;