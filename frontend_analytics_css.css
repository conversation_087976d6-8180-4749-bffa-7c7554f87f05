/* frontend/src/pages/Analytics.css */

.analytics-page {
padding: 2rem 0;
background-color: var(–gray-50);
min-height: calc(100vh - 4rem);
}

.analytics-container {
max-width: 1400px;
margin: 0 auto;
padding: 0 1rem;
}

/* Analytics Header */
.analytics-header {
display: flex;
justify-content: space-between;
align-items: flex-start;
margin-bottom: 2rem;
padding: 2rem;
background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
border-radius: var(–radius-xl);
color: white;
position: relative;
overflow: hidden;
}

.analytics-header::before {
content: ‘’;
position: absolute;
top: 0;
left: 0;
right: 0;
bottom: 0;
background: url(‘data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="analytics-grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23analytics-grid)"/></svg>’);
z-index: 0;
}

.header-content {
position: relative;
z-index: 1;
}

.header-controls {
display: flex;
gap: 1rem;
position: relative;
z-index: 1;
}

.analytics-header h1 {
font-size: 2.5rem;
font-weight: 700;
margin-bottom: 0.5rem;
text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.analytics-header p {
font-size: 1.125rem;
color: rgba(255, 255, 255, 0.9);
margin: 0;
}

.time-range-select {
background-color: rgba(255, 255, 255, 0.1);
border: 1px solid rgba(255, 255, 255, 0.2);
color: white;
border-radius: var(–radius-md);
padding: 0.5rem 1rem;
font-size: 0.875rem;
backdrop-filter: blur(10px);
}

.time-range-select option {
background-color: var(–gray-800);
color: white;
}

/* Overview Grid */
.overview-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
gap: 1.5rem;
margin-bottom: 3rem;
}

.overview-card {
background: white;
border-radius: var(–radius-lg);
padding: 1.5rem;
border: 1px solid var(–gray-200);
box-shadow: var(–shadow-sm);
transition: all var(–transition-fast);
position: relative;
overflow: hidden;
}

.overview-card:hover {
transform: translateY(-2px);
box-shadow: var(–shadow-md);
}

.overview-card::before {
content: ‘’;
position: absolute;
top: 0;
left: 0;
right: 0;
height: 4px;
background: linear-gradient(90deg, var(–primary-500), #8b5cf6);
}

.card-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 1rem;
}

.card-header h3 {
font-size: 0.875rem;
font-weight: 500;
color: var(–gray-600);
margin: 0;
text-transform: uppercase;
letter-spacing: 0.05em;
}

.card-icon {
font-size: 1.5rem;
opacity: 0.7;
}

.card-value {
font-size: 2.5rem;
font-weight: 700;
color: var(–gray-900);
margin-bottom: 0.5rem;
line-height: 1;
}

.card-trend {
display: flex;
align-items: center;
gap: 0.25rem;
font-size: 0.875rem;
font-weight: 500;
}

.card-trend.positive {
color: var(–success-500);
}

.card-trend.negative {
color: var(–error-500);
}

.card-trend span {
font-size: 1rem;
}

/* Charts Grid */
.charts-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
gap: 1.5rem;
margin-bottom: 3rem;
}

.chart-card {
background: white;
border: 1px solid var(–gray-200);
border-radius: var(–radius-lg);
padding: 1.5rem;
box-shadow: var(–shadow-sm);
}

.chart-header {
margin-bottom: 1rem;
}

.chart-header h3 {
font-size: 1.125rem;
font-weight: 600;
color: var(–gray-900);
margin-bottom: 0.25rem;
}

.chart-info {
font-size: 0.875rem;
color: var(–gray-500);
}

.chart-container {
height: 120px;
display: flex;
align-items: end;
padding: 1rem 0;
}

.mini-chart {
display: flex;
align-items: end;
gap: 2px;
width: 100%;
height: 100%;
}

.chart-bar {
flex: 1;
min-height: 4px;
border-radius: 2px;
transition: all var(–transition-fast);
cursor: pointer;
}

.chart-bar:hover {
opacity: 0.8;
transform: scaleY(1.05);
}

/* Details Grid */
.details-grid {
display: grid;
grid-template-columns: 1fr 1fr;
gap: 2rem;
margin-bottom: 3rem;
}

.details-card {
background: white;
border: 1px solid var(–gray-200);
border-radius: var(–radius-lg);
padding: 1.5rem;
box-shadow: var(–shadow-sm);
}

.details-card .card-header {
margin-bottom: 1.5rem;
border-bottom: 1px solid var(–gray-100);
padding-bottom: 1rem;
}

.details-card .card-header h3 {
font-size: 1.25rem;
font-weight: 600;
color: var(–gray-900);
text-transform: none;
letter-spacing: normal;
}

/* Workflows List */
.workflows-list {
display: flex;
flex-direction: column;
gap: 1rem;
}

.workflow-item {
display: flex;
align-items: center;
gap: 1rem;
padding: 1rem;
background-color: var(–gray-50);
border-radius: var(–radius-md);
transition: all var(–transition-fast);
}

.workflow-item:hover {
background-color: var(–gray-100);
}

.workflow-rank {
width: 2rem;
height: 2rem;
border-radius: 50%;
background-color: var(–primary-600);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-weight: 600;
font-size: 0.875rem;
flex-shrink: 0;
}

.workflow-info {
flex: 1;
}

.workflow-name {
font-weight: 600;
color: var(–gray-900);
margin-bottom: 0.25rem;
}

.workflow-stats {
font-size: 0.75rem;
color: var(–gray-500);
}

.workflow-score {
width: 80px;
}

.score-bar {
width: 100%;
height: 6px;
background-color: var(–gray-200);
border-radius: 3px;
overflow: hidden;
}

.score-fill {
height: 100%;
background: linear-gradient(90deg, var(–success-500), var(–success-400));
transition: width var(–transition-normal);
}

/* Cost List */
.cost-list {
display: flex;
flex-direction: column;
gap: 1rem;
margin-bottom: 1.5rem;
}

.cost-item {
display: flex;
align-items: center;
gap: 1rem;
}

.cost-info {
flex: 1;
}

.cost-category {
font-weight: 500;
color: var(–gray-900);
margin-bottom: 0.25rem;
}

.cost-amount {
font-size: 0.875rem;
color: var(–gray-600);
}

.cost-bar {
flex: 2;
height: 8px;
background-color: var(–gray-200);
border-radius: 4px;
overflow: hidden;
}

.cost-fill {
height: 100%;
transition: width var(–transition-normal);
}

.cost-percentage {
min-width: 40px;
text-align: right;
font-size: 0.875rem;
font-weight: 500;
color: var(–gray-600);
}

.cost-summary {
border-top: 1px solid var(–gray-200);
padding-top: 1rem;
display: flex;
flex-direction: column;
gap: 0.5rem;
}

.summary-item {
display: flex;
justify-content: space-between;
align-items: center;
font-size: 0.875rem;
}

.summary-item span {
color: var(–gray-600);
}

.summary-item strong {
color: var(–gray-900);
}

/* Activity Section */
.activity-section {
margin-bottom: 3rem;
}

.section-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 1.5rem;
}

.section-header h3 {
font-size: 1.25rem;
font-weight: 600;
color: var(–gray-900);
}

.activity-list {
background: white;
border: 1px solid var(–gray-200);
border-radius: var(–radius-lg);
overflow: hidden;
}

.activity-item {
display: flex;
align-items: center;
gap: 1rem;
padding: 1rem 1.5rem;
border-bottom: 1px solid var(–gray-100);
transition: background-color var(–transition-fast);
}

.activity-item:last-child {
border-bottom: none;
}

.activity-item:hover {
background-color: var(–gray-50);
}

.activity-status {
width: 2.5rem;
height: 2.5rem;
border-radius: var(–radius-md);
display: flex;
align-items: center;
justify-content: center;
font-size: 1.25rem;
flex-shrink: 0;
}

.activity-status.success {
background-color: var(–success-50);
}

.activity-status.error {
background-color: #fef2f2;
}

.activity-content {
flex: 1;
}

.activity-action {
font-weight: 500;
color: var(–gray-900);
margin-bottom: 0.25rem;
}

.activity-details {
display: flex;
align-items: center;
gap: 1rem;
font-size: 0.875rem;
}

.activity-details .workflow-name {
color: var(–primary-600);
font-weight: 500;
}

.activity-time {
color: var(–gray-500);
}

/* Insights Section */
.insights-section {
margin-bottom: 3rem;
}

.insights-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
gap: 1.5rem;
}

.insight-card {
background: white;
border: 1px solid var(–gray-200);
border-radius: var(–radius-lg);
padding: 1.5rem;
display: flex;
gap: 1rem;
box-shadow: var(–shadow-sm);
transition: all var(–transition-fast);
}

.insight-card:hover {
transform: translateY(-2px);
box-shadow: var(–shadow-md);
}

.insight-icon {
font-size: 2rem;
flex-shrink: 0;
}

.insight-content h4 {
font-size: 1rem;
font-weight: 600;
color: var(–gray-900);
margin-bottom: 0.5rem;
}

.insight-content p {
font-size: 0.875rem;
color: var(–gray-600);
line-height: 1.5;
margin: 0;
}

/* Recommendations Section */
.recommendations-section {
margin-bottom: 3rem;
}

.recommendations-list {
display: flex;
flex-direction: column;
gap: 1rem;
}

.recommendation-item {
background: white;
border: 1px solid var(–gray-200);
border-radius: var(–radius-lg);
padding: 1.5rem;
display: flex;
align-items: center;
gap: 1rem;
box-shadow: var(–shadow-sm);
}

.recommendation-priority {
padding: 0.25rem 0.75rem;
border-radius: 1rem;
font-size: 0.75rem;
font-weight: 600;
text-transform: uppercase;
letter-spacing: 0.05em;
flex-shrink: 0;
}

.recommendation-priority.high {
background-color: #fef2f2;
color: var(–error-600);
border: 1px solid #fecaca;
}

.recommendation-priority.medium {
background-color: #fffbeb;
color: #d97706;
border: 1px solid #fed7aa;
}

.recommendation-priority.low {
background-color: var(–primary-50);
color: var(–primary-700);
border: 1px solid var(–primary-200);
}

.recommendation-content {
flex: 1;
}

.recommendation-content h4 {
font-size: 1rem;
font-weight: 600;
color: var(–gray-900);
margin-bottom: 0.5rem;
}

.recommendation-content p {
font-size: 0.875rem;
color: var(–gray-600);
line-height: 1.5;
margin: 0;
}

.btn-primary.small,
.btn-secondary.small,
.btn-outline.small {
padding: 0.5rem 1rem;
font-size: 0.875rem;
flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
.analytics-page {
padding: 1rem 0;
}

.analytics-container {
padding: 0 1rem;
}

.analytics-header {
flex-direction: column;
gap: 1rem;
padding: 1.5rem;
text-align: center;
}

.analytics-header h1 {
font-size: 2rem;
}

.header-controls {
justify-content: center;
}

.overview-grid,
.charts-grid,
.insights-grid {
grid-template-columns: 1fr;
}

.details-grid {
grid-template-columns: 1fr;
gap: 1.5rem;
}

.recommendation-item {
flex-direction: column;
align-items: flex-start;
gap: 1rem;
}

.activity-details {
flex-direction: column;
align-items: flex-start;
gap: 0.25rem;
}
}

@media (max-width: 480px) {
.analytics-header {
padding: 1rem;
}

.overview-card,
.chart-card,
.details-card,
.insight-card,
.recommendation-item {
padding: 1rem;
}

.card-value {
font-size: 2rem;
}

.workflow-item {
flex-direction: column;
align-items: flex-start;
gap: 0.75rem;
}

.workflow-score {
width: 100%;
}

.cost-item {
flex-direction: column;
align-items: flex-start;
gap: 0.5rem;
}

.cost-bar {
width: 100%;
}
}

/* Loading States */
.chart-container.loading {
display: flex;
align-items: center;
justify-content: center;
}

.chart-container.loading .mini-chart {
display: none;
}

/* Print Styles */
@media print {
.analytics-header {
background: none !important;
color: var(–gray-900) !important;
}

.overview-card,
.chart-card,
.details-card {
break-inside: avoid;
box-shadow: none !important;
border: 1px solid var(–gray-300) !important;
}

.header-controls {
display: none;
}
}