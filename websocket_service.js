// frontend/src/services/websocket.js
class WebSocketService {
constructor() {
this.ws = null;
this.listeners = new Map();
this.reconnectAttempts = 0;
this.maxReconnectAttempts = 5;
this.reconnectInterval = 1000;
this.isConnected = false;
}

connect(token) {
const wsUrl = process.env.REACT_APP_WS_URL || ‘ws://localhost:8000’;

```
try {
  this.ws = new WebSocket(`${wsUrl}/ws?token=${token}`);
  
  this.ws.onopen = () => {
    console.log('WebSocket connected');
    this.isConnected = true;
    this.reconnectAttempts = 0;
    this.emit('connected');
  };

  this.ws.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);
      this.emit('message', data);
      
      // Emit specific event types
      if (data.type) {
        this.emit(data.type, data);
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  };

  this.ws.onclose = () => {
    console.log('WebSocket disconnected');
    this.isConnected = false;
    this.emit('disconnected');
    this.handleReconnect(token);
  };

  this.ws.onerror = (error) => {
    console.error('WebSocket error:', error);
    this.emit('error', error);
  };
} catch (error) {
  console.error('Failed to connect WebSocket:', error);
}
```

}

disconnect() {
if (this.ws) {
this.ws.close();
this.ws = null;
this.isConnected = false;
}
}

send(data) {
if (this.ws && this.ws.readyState === WebSocket.OPEN) {
this.ws.send(JSON.stringify(data));
} else {
console.warn(‘WebSocket is not connected’);
}
}

on(event, callback) {
if (!this.listeners.has(event)) {
this.listeners.set(event, []);
}
this.listeners.get(event).push(callback);
}

off(event, callback) {
if (this.listeners.has(event)) {
const callbacks = this.listeners.get(event);
const index = callbacks.indexOf(callback);
if (index > -1) {
callbacks.splice(index, 1);
}
}
}

emit(event, data) {
if (this.listeners.has(event)) {
this.listeners.get(event).forEach(callback => {
try {
callback(data);
} catch (error) {
console.error(‘Error in WebSocket listener:’, error);
}
});
}
}

handleReconnect(token) {
if (this.reconnectAttempts < this.maxReconnectAttempts) {
setTimeout(() => {
console.log(`Attempting to reconnect WebSocket (${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`);
this.reconnectAttempts++;
this.connect(token);
}, this.reconnectInterval * Math.pow(2, this.reconnectAttempts));
}
}
}

// Create singleton instance
const websocketService = new WebSocketService();

export default websocketService;

// React hook for using WebSocket
export const useWebSocket = () => {
const [isConnected, setIsConnected] = React.useState(websocketService.isConnected);

React.useEffect(() => {
const handleConnect = () => setIsConnected(true);
const handleDisconnect = () => setIsConnected(false);

```
websocketService.on('connected', handleConnect);
websocketService.on('disconnected', handleDisconnect);

return () => {
  websocketService.off('connected', handleConnect);
  websocketService.off('disconnected', handleDisconnect);
};
```

}, []);

return {
isConnected,
send: websocketService.send.bind(websocketService),
on: websocketService.on.bind(websocketService),
off: websocketService.off.bind(websocketService)
};
};