import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from './frontend_auth_context_fixed';

const Landing = () => {
  const { isAuthenticated } = useAuth();

  return (
    <div className="landing-page">
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-content">
          <h1 className="hero-title">
            Transform Ideas into 
            <span className="highlight"> AI Automations</span>
          </h1>
          <p className="hero-subtitle">
            Build powerful automation workflows using natural language. 
            No coding required - just describe what you want to automate.
          </p>
          
          <div className="hero-actions">
            {isAuthenticated ? (
              <Link to="/dashboard" className="btn btn-primary">
                Go to Dashboard
              </Link>
            ) : (
              <>
                <Link to="/register" className="btn btn-primary">
                  Get Started Free
                </Link>
                <Link to="/login" className="btn btn-secondary">
                  Sign In
                </Link>
              </>
            )}
          </div>
        </div>
        
        <div className="hero-visual">
          <div className="automation-demo">
            <div className="demo-block email">📧 Email Reader</div>
            <div className="demo-arrow">→</div>
            <div className="demo-block ai">🤖 AI Generator</div>
            <div className="demo-arrow">→</div>
            <div className="demo-block send">📤 Auto Reply</div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features">
        <div className="container">
          <h2 className="section-title">Powerful Features</h2>
          
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">🚀</div>
              <h3>Ultra-Fast AI</h3>
              <p>Powered by Cerebras AI with 2,600+ tokens/second processing speed</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">🎯</div>
              <h3>Natural Language</h3>
              <p>Just describe what you want - our NLP engine does the rest</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">🔧</div>
              <h3>Visual Builder</h3>
              <p>Drag-and-drop workflow builder with 13+ automation blocks</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">📊</div>
              <h3>Real-time Analytics</h3>
              <p>Monitor performance and track automation success rates</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">🔐</div>
              <h3>Enterprise Ready</h3>
              <p>Built with security, scalability, and reliability in mind</p>
            </div>
            
            <div className="feature-card">
              <div className="feature-icon">⚡</div>
              <h3>One-Click Deploy</h3>
              <p>Deploy your automations instantly with Docker support</p>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="use-cases">
        <div className="container">
          <h2 className="section-title">Popular Use Cases</h2>
          
          <div className="use-cases-grid">
            <div className="use-case">
              <h3>📧 Email Automation</h3>
              <p>Automatically read, process, and respond to customer emails with AI-generated replies.</p>
            </div>
            
            <div className="use-case">
              <h3>📱 Social Media Management</h3>
              <p>Monitor trends, generate content, and schedule posts across multiple platforms.</p>
            </div>
            
            <div className="use-case">
              <h3>📊 Business Reporting</h3>
              <p>Generate daily, weekly, or monthly reports with data analysis and insights.</p>
            </div>
            
            <div className="use-case">
              <h3>🔔 Smart Notifications</h3>
              <p>Set up intelligent alerts and notifications based on specific conditions.</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to Automate Your Workflow?</h2>
            <p>Join thousands of users already saving time with AI automation</p>
            
            {!isAuthenticated && (
              <div className="cta-actions">
                <Link to="/register" className="btn btn-primary btn-large">
                  Start Building Now
                </Link>
                <p className="cta-note">Free to get started • No credit card required</p>
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  );
};

export default Landing;
