// frontend/src/pages/Workflows.js
import React, { useState, useEffect } from ‘react’;
import { Link, useNavigate } from ‘react-router-dom’;
import { useWorkflow } from ‘../contexts/WorkflowContext’;
import { toast } from ‘react-toastify’;
import LoadingSpinner from ‘../components/LoadingSpinner’;
import ‘./Workflows.css’;

const Workflows = () => {
const {
workflows,
fetchWorkflows,
deleteWorkflow,
executeWorkflow,
loading
} = useWorkflow();

const [searchTerm, setSearchTerm] = useState(’’);
const [filterCategory, setFilterCategory] = useState(‘all’);
const [filterStatus, setFilterStatus] = useState(‘all’);
const [sortBy, setSortBy] = useState(‘updated_at’);
const [sortOrder, setSortOrder] = useState(‘desc’);
const [selectedWorkflows, setSelectedWorkflows] = useState([]);
const [viewMode, setViewMode] = useState(‘grid’);

const navigate = useNavigate();

useEffect(() => {
fetchWorkflows();
}, [fetchWorkflows]);

const categories = [
{ id: ‘all’, name: ‘All Categories’, icon: ‘📦’ },
{ id: ‘communication’, name: ‘Communication’, icon: ‘📧’ },
{ id: ‘social_media’, name: ‘Social Media’, icon: ‘📱’ },
{ id: ‘data’, name: ‘Data & Analytics’, icon: ‘📊’ },
{ id: ‘ai_generated’, name: ‘AI Generated’, icon: ‘🧠’ },
{ id: ‘automation’, name: ‘Automation’, icon: ‘🔧’ },
{ id: ‘monitoring’, name: ‘Monitoring’, icon: ‘👁️’ }
];

const sortOptions = [
{ value: ‘updated_at’, label: ‘Last Updated’ },
{ value: ‘created_at’, label: ‘Date Created’ },
{ value: ‘name’, label: ‘Name’ },
{ value: ‘category’, label: ‘Category’ }
];

// Filter and sort workflows
const filteredWorkflows = workflows
.filter(workflow => {
const matchesSearch = workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
workflow.description?.toLowerCase().includes(searchTerm.toLowerCase());
const matchesCategory = filterCategory === ‘all’ || workflow.category === filterCategory;
const matchesStatus = filterStatus === ‘all’ ||
(filterStatus === ‘active’ && workflow.is_active) ||
(filterStatus === ‘inactive’ && !workflow.is_active);

```
  return matchesSearch && matchesCategory && matchesStatus;
})
.sort((a, b) => {
  let aValue = a[sortBy];
  let bValue = b[sortBy];
  
  if (sortBy === 'name') {
    aValue = aValue.toLowerCase();
    bValue = bValue.toLowerCase();
  }
  
  if (sortOrder === 'asc') {
    return aValue > bValue ? 1 : -1;
  } else {
    return aValue < bValue ? 1 : -1;
  }
});
```

const handleExecuteWorkflow = async (workflowId, workflowName) => {
try {
await executeWorkflow(workflowId);
toast.success(`Workflow "${workflowName}" execution started`);
} catch (error) {
toast.error(‘Failed to execute workflow’);
}
};

const handleDeleteWorkflow = async (workflowId, workflowName) => {
if (window.confirm(`Are you sure you want to delete "${workflowName}"? This action cannot be undone.`)) {
try {
await deleteWorkflow(workflowId);
toast.success(`Workflow "${workflowName}" deleted successfully`);
} catch (error) {
toast.error(‘Failed to delete workflow’);
}
}
};

const handleBulkAction = async (action) => {
if (selectedWorkflows.length === 0) {
toast.warning(‘Please select workflows first’);
return;
}

```
if (action === 'delete') {
  if (window.confirm(`Are you sure you want to delete ${selectedWorkflows.length} workflow(s)?`)) {
    try {
      for (const workflowId of selectedWorkflows) {
        await deleteWorkflow(workflowId);
      }
      setSelectedWorkflows([]);
      toast.success(`${selectedWorkflows.length} workflow(s) deleted successfully`);
    } catch (error) {
      toast.error('Failed to delete some workflows');
    }
  }
}
```

};

const toggleWorkflowSelection = (workflowId) => {
setSelectedWorkflows(prev =>
prev.includes(workflowId)
? prev.filter(id => id !== workflowId)
: […prev, workflowId]
);
};

const getCategoryIcon = (category) => {
const categoryObj = categories.find(cat => cat.id === category);
return categoryObj ? categoryObj.icon : ‘🔧’;
};

const formatDate = (dateString) => {
return new Date(dateString).toLocaleDateString(‘en-US’, {
year: ‘numeric’,
month: ‘short’,
day: ‘numeric’,
hour: ‘2-digit’,
minute: ‘2-digit’
});
};

if (loading && workflows.length === 0) {
return <LoadingSpinner message="Loading workflows..." />;
}

return (
<div className="workflows-page">
<div className="workflows-container">
{/* Header */}
<div className="workflows-header">
<div className="header-content">
<h1>🔧 Workflows</h1>
<p>Manage and monitor your automation workflows</p>
</div>

```
      <div className="header-actions">
        <Link to="/nlp-builder" className="btn-secondary">
          <span className="btn-icon">🧠</span>
          NLP Builder
        </Link>
        <button 
          className="btn-primary"
          onClick={() => navigate('/workflows/new')}
        >
          <span className="btn-icon">➕</span>
          New Workflow
        </button>
      </div>
    </div>

    {/* Filters and Search */}
    <div className="workflows-filters">
      <div className="filters-left">
        <div className="search-box">
          <input
            type="text"
            placeholder="Search workflows..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
          <span className="search-icon">🔍</span>
        </div>
        
        <select
          value={filterCategory}
          onChange={(e) => setFilterCategory(e.target.value)}
          className="filter-select"
        >
          {categories.map(category => (
            <option key={category.id} value={category.id}>
              {category.icon} {category.name}
            </option>
          ))}
        </select>
        
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="filter-select"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>
      
      <div className="filters-right">
        <select
          value={`${sortBy}-${sortOrder}`}
          onChange={(e) => {
            const [field, order] = e.target.value.split('-');
            setSortBy(field);
            setSortOrder(order);
          }}
          className="sort-select"
        >
          {sortOptions.map(option => (
            <React.Fragment key={option.value}>
              <option value={`${option.value}-desc`}>
                {option.label} (Newest)
              </option>
              <option value={`${option.value}-asc`}>
                {option.label} (Oldest)
              </option>
            </React.Fragment>
          ))}
        </select>
        
        <div className="view-toggle">
          <button
            className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}
            onClick={() => setViewMode('grid')}
            title="Grid View"
          >
            ⚏
          </button>
          <button
            className={`view-btn ${viewMode === 'list' ? 'active' : ''}`}
            onClick={() => setViewMode('list')}
            title="List View"
          >
            ☰
          </button>
        </div>
      </div>
    </div>

    {/* Bulk Actions */}
    {selectedWorkflows.length > 0 && (
      <div className="bulk-actions">
        <div className="bulk-info">
          {selectedWorkflows.length} workflow(s) selected
        </div>
        <div className="bulk-buttons">
          <button
            className="btn-danger"
            onClick={() => handleBulkAction('delete')}
          >
            Delete Selected
          </button>
        </div>
      </div>
    )}

    {/* Workflows Grid/List */}
    {filteredWorkflows.length > 0 ? (
      <div className={`workflows-content ${viewMode}`}>
        {filteredWorkflows.map(workflow => (
          <div key={workflow.id} className="workflow-item">
            <div className="workflow-header">
              <div className="workflow-select">
                <input
                  type="checkbox"
                  checked={selectedWorkflows.includes(workflow.id)}
                  onChange={() => toggleWorkflowSelection(workflow.id)}
                />
              </div>
              
              <div className="workflow-icon">
                {getCategoryIcon(workflow.category)}
              </div>
              
              <div className="workflow-status">
                <span className={`status-badge ${workflow.is_active ? 'active' : 'inactive'}`}>
                  {workflow.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>
            
            <div className="workflow-content">
              <Link to={`/workflows/${workflow.id}`} className="workflow-title">
                {workflow.name}
              </Link>
              <p className="workflow-description">
                {workflow.description || 'No description provided'}
              </p>
              
              <div className="workflow-meta">
                <div className="meta-item">
                  <span className="meta-icon">🔗</span>
                  <span>{workflow.blocks?.length || 0} blocks</span>
                </div>
                <div className="meta-item">
                  <span className="meta-icon">📂</span>
                  <span>{workflow.category.replace('_', ' ')}</span>
                </div>
                <div className="meta-item">
                  <span className="meta-icon">📅</span>
                  <span>{formatDate(workflow.updated_at)}</span>
                </div>
              </div>
            </div>
            
            <div className="workflow-actions">
              <button
                className="action-btn"
                onClick={() => handleExecuteWorkflow(workflow.id, workflow.name)}
                title="Execute Workflow"
                disabled={!workflow.is_active}
              >
                ▶️
              </button>
              <Link
                to={`/workflows/${workflow.id}`}
                className="action-btn"
                title="Edit Workflow"
              >
                ✏️
              </Link>
              <button
                className="action-btn delete"
                onClick={() => handleDeleteWorkflow(workflow.id, workflow.name)}
                title="Delete Workflow"
              >
                🗑️
              </button>
            </div>
          </div>
        ))}
      </div>
    ) : (
      <div className="empty-workflows">
        {workflows.length === 0 ? (
          <>
            <div className="empty-icon">🚀</div>
            <h3>No workflows yet</h3>
            <p>Create your first automation workflow to get started</p>
            <div className="empty-actions">
              <Link to="/nlp-builder" className="btn-primary">
                <span className="btn-icon">🧠</span>
                Create with NLP
              </Link>
              <button 
                className="btn-secondary"
                onClick={() => navigate('/workflows/new')}
              >
                <span className="btn-icon">🔧</span>
                Visual Builder
              </button>
            </div>
          </>
        ) : (
          <>
            <div className="empty-icon">🔍</div>
            <h3>No workflows found</h3>
            <p>Try adjusting your search or filter criteria</p>
            <button 
              className="btn-secondary"
              onClick={() => {
                setSearchTerm('');
                setFilterCategory('all');
                setFilterStatus('all');
              }}
            >
              Clear Filters
            </button>
          </>
        )}
      </div>
    )}

    {/* Workflow Stats */}
    <div className="workflows-stats">
      <div className="stats-header">
        <h3>Workflow Statistics</h3>
      </div>
      
      <div className="stats-grid">
        <div className="stat-item">
          <div className="stat-number">{workflows.length}</div>
          <div className="stat-label">Total Workflows</div>
        </div>
        <div className="stat-item">
          <div className="stat-number">{workflows.filter(w => w.is_active).length}</div>
          <div className="stat-label">Active</div>
        </div>
        <div className="stat-item">
          <div className="stat-number">{workflows.filter(w => w.category === 'ai_generated').length}</div>
          <div className="stat-label">AI Generated</div>
        </div>
        <div className="stat-item">
          <div className="stat-number">
            {Math.round(workflows.filter(w => w.is_active).length / Math.max(workflows.length, 1) * 100)}%
          </div>
          <div className="stat-label">Active Rate</div>
        </div>
      </div>
    </div>

    {/* Quick Tips */}
    <div className="workflows-tips">
      <div className="tips-header">
        <h3>💡 Quick Tips</h3>
      </div>
      
      <div className="tips-list">
        <div className="tip-item">
          <span className="tip-icon">🧠</span>
          <span>Use the NLP Builder to create workflows from natural language descriptions</span>
        </div>
        <div className="tip-item">
          <span className="tip-icon">🔗</span>
          <span>Connect multiple blocks to create complex automation workflows</span>
        </div>
        <div className="tip-item">
          <span className="tip-icon">📊</span>
          <span>Monitor workflow performance in the Analytics section</span>
        </div>
        <div className="tip-item">
          <span className="tip-icon">⚡</span>
          <span>Active workflows can be executed manually or triggered by schedules</span>
        </div>
      </div>
    </div>
  </div>
</div>
```

);
};

export default Workflows;