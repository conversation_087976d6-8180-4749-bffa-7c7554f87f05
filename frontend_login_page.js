// frontend/src/pages/Login.js
import React, { useState, useEffect } from ‘react’;
import { Link, useNavigate, useLocation } from ‘react-router-dom’;
import { useAuth } from ‘../contexts/AuthContext’;
import ‘./Auth.css’;

const Login = () => {
const [formData, setFormData] = useState({
email: ‘’,
password: ‘’
});
const [loading, setLoading] = useState(false);
const [errors, setErrors] = useState({});

const { login, isAuthenticated } = useAuth();
const navigate = useNavigate();
const location = useLocation();

// Redirect if already authenticated
useEffect(() => {
if (isAuthenticated) {
const from = location.state?.from?.pathname || ‘/dashboard’;
navigate(from, { replace: true });
}
}, [isAuthenticated, navigate, location]);

const handleChange = (e) => {
const { name, value } = e.target;
setFormData(prev => ({
…prev,
[name]: value
}));

```
// Clear error for this field
if (errors[name]) {
  setErrors(prev => ({
    ...prev,
    [name]: ''
  }));
}
```

};

const validateForm = () => {
const newErrors = {};

```
if (!formData.email) {
  newErrors.email = 'Email is required';
} else if (!/\S+@\S+\.\S+/.test(formData.email)) {
  newErrors.email = 'Please enter a valid email';
}

if (!formData.password) {
  newErrors.password = 'Password is required';
} else if (formData.password.length < 6) {
  newErrors.password = 'Password must be at least 6 characters';
}

setErrors(newErrors);
return Object.keys(newErrors).length === 0;
```

};

const handleSubmit = async (e) => {
e.preventDefault();

```
if (!validateForm()) {
  return;
}

setLoading(true);

try {
  const result = await login(formData.email, formData.password);
  
  if (result.success) {
    const from = location.state?.from?.pathname || '/dashboard';
    navigate(from, { replace: true });
  } else {
    setErrors({ submit: result.error });
  }
} catch (error) {
  setErrors({ submit: 'An unexpected error occurred' });
} finally {
  setLoading(false);
}
```

};

const handleDemoLogin = async () => {
setLoading(true);
setFormData({
email: ‘<EMAIL>’,
password: ‘password123’
});

```
try {
  const result = await login('<EMAIL>', 'password123');
  if (result.success) {
    navigate('/dashboard', { replace: true });
  }
} catch (error) {
  setErrors({ submit: 'Demo login failed' });
} finally {
  setLoading(false);
}
```

};

return (
<div className="auth-page">
<div className="auth-container">
<div className="auth-header">
<Link to="/" className="auth-logo">
<span className="logo-icon">🤖</span>
<span className="logo-text">AutoGPT</span>
</Link>
<h1>Welcome back</h1>
<p>Sign in to your AutoGPT account</p>
</div>

```
    <form onSubmit={handleSubmit} className="auth-form">
      {errors.submit && (
        <div className="error-banner">
          {errors.submit}
        </div>
      )}

      <div className="form-group">
        <label htmlFor="email">Email address</label>
        <input
          type="email"
          id="email"
          name="email"
          value={formData.email}
          onChange={handleChange}
          placeholder="Enter your email"
          className={errors.email ? 'error' : ''}
          disabled={loading}
        />
        {errors.email && <span className="error-text">{errors.email}</span>}
      </div>

      <div className="form-group">
        <label htmlFor="password">Password</label>
        <input
          type="password"
          id="password"
          name="password"
          value={formData.password}
          onChange={handleChange}
          placeholder="Enter your password"
          className={errors.password ? 'error' : ''}
          disabled={loading}
        />
        {errors.password && <span className="error-text">{errors.password}</span>}
      </div>

      <div className="form-options">
        <label className="checkbox-label">
          <input type="checkbox" />
          <span className="checkmark"></span>
          Remember me
        </label>
        <Link to="/forgot-password" className="forgot-link">
          Forgot password?
        </Link>
      </div>

      <button 
        type="submit" 
        className="btn-primary full-width"
        disabled={loading}
      >
        {loading ? (
          <>
            <span className="loading-spinner small"></span>
            Signing in...
          </>
        ) : (
          'Sign In'
        )}
      </button>

      <div className="auth-divider">
        <span>or</span>
      </div>

      <button
        type="button"
        onClick={handleDemoLogin}
        className="btn-secondary full-width"
        disabled={loading}
      >
        <span className="btn-icon">🎮</span>
        Try Demo Account
      </button>
    </form>

    <div className="auth-footer">
      <p>
        Don't have an account?{' '}
        <Link to="/register" className="auth-link">
          Sign up for free
        </Link>
      </p>
    </div>

    <div className="auth-features">
      <div className="feature-item">
        <span className="feature-icon">⚡</span>
        <span>Ultra-fast Cerebras AI</span>
      </div>
      <div className="feature-item">
        <span className="feature-icon">🧠</span>
        <span>Natural Language Processing</span>
      </div>
      <div className="feature-item">
        <span className="feature-icon">🔒</span>
        <span>Enterprise Security</span>
      </div>
    </div>
  </div>

  <div className="auth-visual">
    <div className="visual-content">
      <h2>Build AI Automations with Natural Language</h2>
      <p>
        "Create an email auto-responder that uses AI to reply to customers"
        → Complete workflow in seconds
      </p>
      
      <div className="feature-showcase">
        <div className="showcase-item">
          <div className="showcase-icon">📈</div>
          <div className="showcase-text">
            <strong>2,600+ tokens/sec</strong>
            <span>20x faster than traditional GPU inference</span>
          </div>
        </div>
        
        <div className="showcase-item">
          <div className="showcase-icon">🎯</div>
          <div className="showcase-text">
            <strong>94% accuracy</strong>
            <span>Advanced NLP understands your intent</span>
          </div>
        </div>
        
        <div className="showcase-item">
          <div className="showcase-icon">🔧</div>
          <div className="showcase-text">
            <strong>13+ block types</strong>
            <span>Email, AI, web scraping, social media & more</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
```

);
};

export default Login;