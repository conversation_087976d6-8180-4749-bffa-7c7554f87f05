import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useWorkflow } from './frontend_workflow_context_fixed';

const Workflows = () => {
  const { 
    workflows, 
    isLoading, 
    deleteWorkflow, 
    executeWorkflow, 
    fetchWorkflows 
  } = useWorkflow();
  
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [executingWorkflows, setExecutingWorkflows] = useState(new Set());

  useEffect(() => {
    fetchWorkflows();
  }, []);

  const filteredWorkflows = workflows.filter(workflow => {
    const matchesFilter = filter === 'all' || 
      (filter === 'active' && workflow.is_active) ||
      (filter === 'inactive' && !workflow.is_active);
    
    const matchesSearch = workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (workflow.description && workflow.description.toLowerCase().includes(searchTerm.toLowerCase()));
    
    return matchesFilter && matchesSearch;
  });

  const handleExecute = async (workflowId) => {
    setExecutingWorkflows(prev => new Set([...prev, workflowId]));
    
    try {
      const result = await executeWorkflow(workflowId);
      if (result.success) {
        // Show success message (you might want to add a toast notification here)
        console.log('Workflow executed successfully');
      } else {
        console.error('Workflow execution failed:', result.error);
      }
    } catch (error) {
      console.error('Error executing workflow:', error);
    } finally {
      setExecutingWorkflows(prev => {
        const newSet = new Set(prev);
        newSet.delete(workflowId);
        return newSet;
      });
    }
  };

  const handleDelete = async (workflowId) => {
    if (window.confirm('Are you sure you want to delete this workflow?')) {
      const result = await deleteWorkflow(workflowId);
      if (result.success) {
        // Refresh the list (already handled by context)
        console.log('Workflow deleted successfully');
      } else {
        console.error('Failed to delete workflow:', result.error);
      }
    }
  };

  return (
    <div className="workflows-page">
      <div className="page-header">
        <div className="header-content">
          <h1>My Workflows</h1>
          <p>Manage and monitor your automation workflows</p>
        </div>
        
        <div className="header-actions">
          <Link to="/nlp-builder" className="btn btn-primary">
            Create New Workflow
          </Link>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="workflows-controls">
        <div className="search-container">
          <input
            type="text"
            placeholder="Search workflows..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        
        <div className="filter-container">
          <button
            className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
            onClick={() => setFilter('all')}
          >
            All ({workflows.length})
          </button>
          <button
            className={`filter-btn ${filter === 'active' ? 'active' : ''}`}
            onClick={() => setFilter('active')}
          >
            Active ({workflows.filter(w => w.is_active).length})
          </button>
          <button
            className={`filter-btn ${filter === 'inactive' ? 'active' : ''}`}
            onClick={() => setFilter('inactive')}
          >
            Inactive ({workflows.filter(w => !w.is_active).length})
          </button>
        </div>
      </div>

      {/* Workflows List */}
      <div className="workflows-container">
        {isLoading ? (
          <div className="loading-state">
            <div className="spinner"></div>
            <p>Loading workflows...</p>
          </div>
        ) : filteredWorkflows.length > 0 ? (
          <div className="workflows-grid">
            {filteredWorkflows.map(workflow => (
              <div key={workflow.id} className="workflow-card">
                <div className="workflow-header">
                  <h3>{workflow.name}</h3>
                  <div className="workflow-status">
                    <span className={`status-badge ${workflow.is_active ? 'active' : 'inactive'}`}>
                      {workflow.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
                
                <div className="workflow-body">
                  <p className="workflow-description">
                    {workflow.description || 'No description provided'}
                  </p>
                  
                  <div className="workflow-meta">
                    <div className="meta-item">
                      <span className="meta-label">Category:</span>
                      <span className="meta-value">{workflow.category}</span>
                    </div>
                    <div className="meta-item">
                      <span className="meta-label">Created:</span>
                      <span className="meta-value">
                        {new Date(workflow.created_at).toLocaleDateString()}
                      </span>
                    </div>
                    {workflow.tags && workflow.tags.length > 0 && (
                      <div className="workflow-tags">
                        {workflow.tags.map((tag, index) => (
                          <span key={index} className="tag">
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="workflow-actions">
                  <button
                    onClick={() => handleExecute(workflow.id)}
                    disabled={executingWorkflows.has(workflow.id)}
                    className="btn btn-small btn-secondary"
                  >
                    {executingWorkflows.has(workflow.id) ? 'Running...' : 'Execute'}
                  </button>
                  
                  <Link
                    to={`/workflows/${workflow.id}`}
                    className="btn btn-small"
                  >
                    Edit
                  </Link>
                  
                  <button
                    onClick={() => handleDelete(workflow.id)}
                    className="btn btn-small btn-danger"
                  >
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="empty-state">
            <div className="empty-icon">🤖</div>
            <h3>
              {searchTerm ? 'No workflows found' : 'No workflows yet'}
            </h3>
            <p>
              {searchTerm 
                ? 'Try adjusting your search terms or filters' 
                : 'Create your first automation workflow to get started'
              }
            </p>
            {!searchTerm && (
              <Link to="/nlp-builder" className="btn btn-primary">
                Create Your First Workflow
              </Link>
            )}
          </div>
        )}
      </div>

      {/* Stats Summary */}
      {workflows.length > 0 && (
        <div className="workflows-summary">
          <div className="summary-stats">
            <div className="summary-item">
              <span className="summary-number">{workflows.length}</span>
              <span className="summary-label">Total Workflows</span>
            </div>
            <div className="summary-item">
              <span className="summary-number">
                {workflows.filter(w => w.is_active).length}
              </span>
              <span className="summary-label">Active</span>
            </div>
            <div className="summary-item">
              <span className="summary-number">
                {workflows.length * 5} {/* Mock executions */}
              </span>
              <span className="summary-label">Total Executions</span>
            </div>
            <div className="summary-item">
              <span className="summary-number">96.5%</span>
              <span className="summary-label">Success Rate</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Workflows;
