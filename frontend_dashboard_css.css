/* frontend/src/pages/Dashboard.css */

.dashboard {
padding: 2rem 0;
background-color: var(–gray-50);
min-height: calc(100vh - 4rem);
}

.dashboard-container {
max-width: 1400px;
margin: 0 auto;
padding: 0 1rem;
}

/* Dashboard Header */
.dashboard-header {
display: flex;
justify-content: space-between;
align-items: flex-start;
margin-bottom: 2rem;
padding: 2rem;
background: linear-gradient(135deg, var(–primary-600) 0%, #8b5cf6 100%);
border-radius: var(–radius-xl);
color: white;
position: relative;
overflow: hidden;
}

.dashboard-header::before {
content: ‘’;
position: absolute;
top: 0;
left: 0;
right: 0;
bottom: 0;
background: url(‘data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>’);
z-index: 0;
}

.header-content {
position: relative;
z-index: 1;
}

.header-actions {
position: relative;
z-index: 1;
}

.dashboard-header h1 {
font-size: 2.5rem;
font-weight: 700;
margin-bottom: 0.5rem;
text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-header p {
font-size: 1.125rem;
color: rgba(255, 255, 255, 0.9);
margin: 0;
}

/* Stats Grid */
.stats-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
gap: 1.5rem;
margin-bottom: 3rem;
}

.stat-card {
background: white;
border-radius: var(–radius-lg);
padding: 1.5rem;
border: 1px solid var(–gray-200);
box-shadow: var(–shadow-sm);
display: flex;
align-items: center;
gap: 1rem;
transition: all var(–transition-fast);
position: relative;
overflow: hidden;
}

.stat-card:hover {
transform: translateY(-2px);
box-shadow: var(–shadow-md);
}

.stat-card::before {
content: ‘’;
position: absolute;
top: 0;
left: 0;
right: 0;
height: 3px;
background: linear-gradient(90deg, var(–primary-500), #8b5cf6);
}

.stat-icon {
font-size: 2.5rem;
opacity: 0.8;
}

.stat-content {
flex: 1;
}

.stat-number {
font-size: 2rem;
font-weight: 700;
color: var(–gray-900);
margin-bottom: 0.25rem;
}

.stat-label {
font-size: 0.875rem;
color: var(–gray-600);
font-weight: 500;
}

/* Dashboard Section */
.dashboard-section {
margin-bottom: 3rem;
}

.section-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 1.5rem;
}

.section-header h2 {
font-size: 1.5rem;
font-weight: 600;
color: var(–gray-900);
margin: 0;
}

.section-header p {
color: var(–gray-600);
margin: 0;
font-size: 0.875rem;
}

.section-link {
color: var(–primary-600);
text-decoration: none;
font-weight: 500;
font-size: 0.875rem;
padding: 0.5rem 1rem;
border-radius: var(–radius-md);
transition: all var(–transition-fast);
}

.section-link:hover {
background-color: var(–primary-50);
text-decoration: none;
}

/* Quick Actions */
.quick-actions-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
gap: 1.5rem;
}

.quick-action-card {
background: white;
border: 1px solid var(–gray-200);
border-radius: var(–radius-lg);
padding: 1.5rem;
text-decoration: none;
color: inherit;
transition: all var(–transition-fast);
display: flex;
align-items: center;
gap: 1rem;
position: relative;
overflow: hidden;
}

.quick-action-card:hover {
transform: translateY(-4px);
box-shadow: var(–shadow-lg);
text-decoration: none;
border-color: var(–accent-color, var(–primary-500));
}

.quick-action-card::before {
content: ‘’;
position: absolute;
top: 0;
left: 0;
right: 0;
height: 3px;
background-color: var(–accent-color, var(–primary-500));
transform: scaleX(0);
transition: transform var(–transition-fast);
}

.quick-action-card:hover::before {
transform: scaleX(1);
}

.action-icon {
font-size: 2.5rem;
color: var(–accent-color, var(–primary-500));
margin-right: 0.5rem;
}

.action-content {
flex: 1;
}

.action-content h3 {
font-size: 1.125rem;
font-weight: 600;
margin-bottom: 0.5rem;
color: var(–gray-900);
}

.action-content p {
color: var(–gray-600);
font-size: 0.875rem;
margin: 0;
line-height: 1.4;
}

.action-arrow {
font-size: 1.5rem;
color: var(–gray-400);
transition: all var(–transition-fast);
}

.quick-action-card:hover .action-arrow {
color: var(–accent-color, var(–primary-500));
transform: translateX(4px);
}

/* Workflows Grid */
.workflows-grid {
display: grid;
grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
gap: 1.5rem;
}

.workflow-card {
background: white;
border: 1px solid var(–gray-200);
border-radius: var(–radius-lg);
padding: 1.5rem;
text-decoration: none;
color: inherit;
transition: all var(–transition-fast);
display: block;
}

.workflow-card:hover {
transform: translateY(-2px);
box-shadow: var(–shadow-lg);
text-decoration: none;
border-color: var(–primary-300);
}

.workflow-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 1rem;
}

.workflow-icon {
font-size: 2rem;
padding: 0.5rem;
background-color: var(–primary-50);
border-radius: var(–radius-md);
display: flex;
align-items: center;
justify-content: center;
}

.workflow-status {
display: flex;
align-items: center;
}

.status-indicator {
padding: 0.25rem 0.75rem;
border-radius: 1rem;
font-size: 0.75rem;
font-weight: 500;
text-transform: uppercase;
letter-spacing: 0.025em;
}

.status-indicator.active {
background-color: var(–success-500);
color: white;
}

.status-indicator.inactive {
background-color: var(–gray-300);
color: var(–gray-700);
}

.workflow-content h3 {
font-size: 1.125rem;
font-weight: 600;
margin-bottom: 0.5rem;
color: var(–gray-900);
}

.workflow-content p {
color: var(–gray-600);
font-size: 0.875rem;
line-height: 1.4;
margin-bottom: 1rem;
display: -webkit-box;
-webkit-line-clamp: 2;
-webkit-box-orient: vertical;
overflow: hidden;
}

.workflow-meta {
display: flex;
justify-content: space-between;
align-items: center;
font-size: 0.75rem;
color: var(–gray-500);
}

.meta-item {
display: flex;
align-items: center;
gap: 0.25rem;
}

.meta-icon {
font-size: 0.875rem;
}

/* Empty State */
.empty-state {
text-align: center;
padding: 3rem 2rem;
background: white;
border: 1px solid var(–gray-200);
border-radius: var(–radius-lg);
margin: 2rem 0;
}

.empty-icon {
font-size: 4rem;
margin-bottom: 1rem;
opacity: 0.6;
}

.empty-state h3 {
font-size: 1.25rem;
font-weight: 600;
margin-bottom: 0.5rem;
color: var(–gray-900);
}

.empty-state p {
color: var(–gray-600);
margin-bottom: 1.5rem;
}

/* Getting Started */
.getting-started-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
gap: 1.5rem;
}

.guide-card {
background: white;
border: 1px solid var(–gray-200);
border-radius: var(–radius-lg);
padding: 1.5rem;
text-align: center;
transition: all var(–transition-fast);
}

.guide-card:hover {
transform: translateY(-2px);
box-shadow: var(–shadow-md);
}

.guide-icon {
font-size: 2.5rem;
margin-bottom: 1rem;
display: block;
}

.guide-content h3 {
font-size: 1.125rem;
font-weight: 600;
margin-bottom: 0.75rem;
color: var(–gray-900);
}

.guide-content p {
color: var(–gray-600);
font-size: 0.875rem;
margin-bottom: 1rem;
line-height: 1.4;
}

.guide-button {
background-color: var(–primary-600);
color: white;
border: none;
padding: 0.5rem 1rem;
border-radius: var(–radius-md);
font-weight: 500;
font-size: 0.875rem;
cursor: pointer;
transition: all var(–transition-fast);
}

.guide-button:hover {
background-color: var(–primary-700);
transform: translateY(-1px);
}

/* Activity List */
.activity-list {
background: white;
border: 1px solid var(–gray-200);
border-radius: var(–radius-lg);
overflow: hidden;
}

.activity-item {
display: flex;
align-items: center;
gap: 1rem;
padding: 1rem 1.5rem;
border-bottom: 1px solid var(–gray-100);
transition: background-color var(–transition-fast);
}

.activity-item:last-child {
border-bottom: none;
}

.activity-item:hover {
background-color: var(–gray-50);
}

.activity-icon {
font-size: 1.5rem;
width: 2.5rem;
height: 2.5rem;
border-radius: var(–radius-md);
background-color: var(–primary-50);
display: flex;
align-items: center;
justify-content: center;
flex-shrink: 0;
}

.activity-content {
flex: 1;
}

.activity-title {
font-weight: 500;
color: var(–gray-900);
font-size: 0.875rem;
margin-bottom: 0.25rem;
}

.activity-time {
font-size: 0.75rem;
color: var(–gray-500);
}

/* Performance Grid */
.performance-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
gap: 1.5rem;
}

.performance-card {
background: white;
border: 1px solid var(–gray-200);
border-radius: var(–radius-lg);
padding: 1.5rem;
transition: all var(–transition-fast);
}

.performance-card:hover {
transform: translateY(-2px);
box-shadow: var(–shadow-md);
}

.performance-metric {
margin-bottom: 1rem;
}

.metric-value {
font-size: 1.75rem;
font-weight: 700;
color: var(–gray-900);
margin-bottom: 0.25rem;
}

.metric-label {
font-size: 0.875rem;
color: var(–gray-600);
font-weight: 500;
}

.metric-change {
display: flex;
align-items: center;
gap: 0.25rem;
font-size: 0.75rem;
font-weight: 500;
}

.metric-change.positive {
color: var(–success-500);
}

.metric-change.negative {
color: var(–error-500);
}

.metric-change span {
font-size: 0.875rem;
}

/* Loading Spinner Container */
.loading-container {
display: flex;
flex-direction: column;
align-items: center;
justify-content: center;
padding: 4rem 2rem;
text-align: center;
}

.loading-message {
margin-top: 1rem;
color: var(–gray-600);
font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
.dashboard {
padding: 1rem 0;
}

.dashboard-container {
padding: 0 1rem;
}

.dashboard-header {
flex-direction: column;
gap: 1rem;
padding: 1.5rem;
text-align: center;
}

.dashboard-header h1 {
font-size: 2rem;
}

.stats-grid {
grid-template-columns: 1fr;
gap: 1rem;
}

.section-header {
flex-direction: column;
align-items: flex-start;
gap: 0.5rem;
}

.quick-actions-grid,
.workflows-grid,
.getting-started-grid,
.performance-grid {
grid-template-columns: 1fr;
}

.quick-action-card {
flex-direction: column;
text-align: center;
gap: 1rem;
}

.action-arrow {
display: none;
}
}

@media (max-width: 480px) {
.dashboard-header {
padding: 1rem;
}

.stat-card,
.quick-action-card,
.workflow-card,
.guide-card,
.performance-card {
padding: 1rem;
}

.empty-state {
padding: 2rem 1rem;
}

.activity-item {
padding: 0.75rem 1rem;
}
}

/* Print Styles */
@media print {
.dashboard-header {
background: none !important;
color: var(–gray-900) !important;
}

.stat-card,
.workflow-card,
.performance-card {
break-inside: avoid;
}
}