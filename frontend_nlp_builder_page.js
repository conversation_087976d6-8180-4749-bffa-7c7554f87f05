// frontend/src/pages/NLPBuilder.js
import React, { useState } from ‘react’;
import { useNavigate } from ‘react-router-dom’;
import { useWorkflow } from ‘../contexts/WorkflowContext’;
import { toast } from ‘react-toastify’;
import ‘./NLPBuilder.css’;

const NLPBuilder = () => {
const [description, setDescription] = useState(’’);
const [processing, setProcessing] = useState(false);
const [result, setResult] = useState(null);
const [creating, setCreating] = useState(false);
const [selectedExample, setSelectedExample] = useState(null);

const { processNLPDescription, createWorkflowFromNLP } = useWorkflow();
const navigate = useNavigate();

const examples = [
{
title: ‘Email Auto-Responder’,
description: ‘Create an agent that monitors my Gmail inbox for customer support emails, generates helpful responses using AI, and automatically sends them back to customers.’,
category: ‘Communication’,
icon: ‘📧’,
blocks: [‘Email Reader’, ‘AI Generator’, ‘Email Sender’]
},
{
title: ‘Social Media Manager’,
description: ‘Build a workflow that checks trending topics on Reddit every day, creates engaging social media posts about them, and schedules them to post on Twitter and LinkedIn.’,
category: ‘Social Media’,
icon: ‘📱’,
blocks: [‘Web Scraper’, ‘AI Content’, ‘Social Poster’]
},
{
title: ‘Daily Report Generator’,
description: ‘Make an automation that reads my sales data every morning, generates a daily report with insights using AI, and emails it to my team at 9 AM.’,
category: ‘Analytics’,
icon: ‘📊’,
blocks: [‘Scheduler’, ‘File Reader’, ‘AI Analysis’, ‘Email Sender’]
},
{
title: ‘Content Creator’,
description: ‘Create an agent that monitors YouTube for trending videos in my niche, extracts the best quotes, and automatically creates short-form content for TikTok.’,
category: ‘Content’,
icon: ‘🎥’,
blocks: [‘Web Monitor’, ‘AI Extractor’, ‘Content Creator’]
},
{
title: ‘Price Monitor’,
description: ‘Monitor competitor prices on their websites every hour and send me a notification when any price drops below our pricing threshold.’,
category: ‘Monitoring’,
icon: ‘💰’,
blocks: [‘Web Scraper’, ‘Conditional’, ‘Notification’]
},
{
title: ‘Lead Qualification’,
description: ‘Automatically read new form submissions, use AI to qualify leads based on their responses, and add qualified leads to our CRM system.’,
category: ‘Sales’,
icon: ‘🎯’,
blocks: [‘Form Reader’, ‘AI Qualifier’, ‘CRM Integration’]
}
];

const handleExampleClick = (example) => {
setSelectedExample(example);
setDescription(example.description);
setResult(null);
};

const handleProcess = async () => {
if (!description.trim()) {
toast.error(‘Please enter a description of your automation’);
return;
}

```
if (description.trim().length < 20) {
  toast.error('Please provide a more detailed description (at least 20 characters)');
  return;
}

setProcessing(true);
setResult(null);

try {
  const nlpResult = await processNLPDescription(description.trim());
  setResult(nlpResult);
  
  if (nlpResult.confidence < 0.6) {
    toast.warning('Low confidence in automation detection. Consider providing more details.');
  } else {
    toast.success(`Automation generated with ${(nlpResult.confidence * 100).toFixed(1)}% confidence!`);
  }
} catch (error) {
  toast.error('Failed to process description. Please try again.');
} finally {
  setProcessing(false);
}
```

};

const handleCreateWorkflow = async () => {
if (!result) return;

```
setCreating(true);
try {
  const workflow = await createWorkflowFromNLP(result);
  toast.success(`Workflow "${workflow.name}" created successfully!`);
  navigate(`/workflows/${workflow.id}`);
} catch (error) {
  toast.error('Failed to create workflow. Please try again.');
} finally {
  setCreating(false);
}
```

};

const handleClear = () => {
setDescription(’’);
setResult(null);
setSelectedExample(null);
};

return (
<div className="nlp-builder">
<div className="nlp-container">
{/* Header */}
<div className="nlp-header">
<h1>🧠 Natural Language Builder</h1>
<p>Describe your automation in plain English and watch AutoGPT build it instantly</p>

```
      <div className="nlp-features">
        <div className="feature-badge">
          <span className="badge-icon">⚡</span>
          <span>Ultra-fast Cerebras AI</span>
        </div>
        <div className="feature-badge">
          <span className="badge-icon">🎯</span>
          <span>94% accuracy</span>
        </div>
        <div className="feature-badge">
          <span className="badge-icon">🔗</span>
          <span>13+ block types</span>
        </div>
      </div>
    </div>

    {/* Main Content */}
    <div className="nlp-content">
      {/* Input Section */}
      <div className="nlp-input-section">
        <div className="input-header">
          <h2>Describe Your Automation</h2>
          <p>Tell us what you want to automate in natural language</p>
        </div>

        <div className="input-container">
          <textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Example: Create an agent that monitors my Gmail for customer emails, generates AI responses, and sends them back automatically..."
            className="description-input"
            rows={6}
            maxLength={2000}
          />
          
          <div className="input-footer">
            <div className="char-count">
              {description.length}/2000 characters
            </div>
            <div className="input-actions">
              <button 
                onClick={handleClear} 
                className="btn-secondary"
                disabled={!description && !result}
              >
                Clear
              </button>
              <button 
                onClick={handleProcess} 
                className="btn-primary"
                disabled={processing || !description.trim()}
              >
                {processing ? (
                  <>
                    <span className="loading-spinner small"></span>
                    Processing...
                  </>
                ) : (
                  <>
                    <span className="btn-icon">🔄</span>
                    Generate Automation
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Examples Section */}
      <div className="nlp-examples-section">
        <div className="examples-header">
          <h3>Try These Examples</h3>
          <p>Click any example to see how AutoGPT transforms natural language into workflows</p>
        </div>

        <div className="examples-grid">
          {examples.map((example, index) => (
            <div
              key={index}
              className={`example-card ${selectedExample === example ? 'selected' : ''}`}
              onClick={() => handleExampleClick(example)}
            >
              <div className="example-header">
                <div className="example-icon">{example.icon}</div>
                <div className="example-category">{example.category}</div>
              </div>
              
              <div className="example-content">
                <h4>{example.title}</h4>
                <p>{example.description}</p>
              </div>
              
              <div className="example-blocks">
                {example.blocks.map((block, blockIndex) => (
                  <span key={blockIndex} className="block-tag">{block}</span>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Results Section */}
      {result && (
        <div className="nlp-results-section">
          <div className="results-header">
            <h3>Generated Automation</h3>
            <div className="confidence-score">
              <span className="confidence-label">Confidence:</span>
              <div className="confidence-bar">
                <div 
                  className="confidence-fill"
                  style={{
                    width: `${result.confidence * 100}%`,
                    backgroundColor: result.confidence > 0.8 ? '#10b981' : 
                                   result.confidence > 0.6 ? '#f59e0b' : '#ef4444'
                  }}
                ></div>
              </div>
              <span className="confidence-value">{(result.confidence * 100).toFixed(1)}%</span>
            </div>
          </div>

          <div className="results-content">
            {/* Workflow Overview */}
            <div className="workflow-overview">
              <div className="overview-card">
                <h4>📝 Suggested Name</h4>
                <p>{result.suggested_name}</p>
              </div>
              
              <div className="overview-card">
                <h4>📄 Description</h4>
                <p>{result.suggested_description}</p>
              </div>
            </div>

            {/* Generated Blocks */}
            <div className="generated-blocks">
              <h4>🔗 Automation Blocks ({result.blocks.length})</h4>
              <div className="blocks-flow">
                {result.blocks.map((block, index) => (
                  <React.Fragment key={index}>
                    <div className="block-item">
                      <div className="block-icon">
                        {block.type === 'email_reader' ? '📧' :
                         block.type === 'email_sender' ? '📤' :
                         block.type === 'ai_text_generator' ? '🤖' :
                         block.type === 'web_scraper' ? '🕷️' :
                         block.type === 'social_poster' ? '📱' :
                         block.type === 'file_reader' ? '📄' :
                         block.type === 'file_writer' ? '💾' :
                         block.type === 'scheduler' ? '⏰' :
                         block.type === 'conditional' ? '🔀' :
                         block.type === 'webhook' ? '🔗' :
                         block.type === 'api_call' ? '🌐' :
                         block.type === 'notification_sender' ? '🔔' :
                         block.type === 'data_processor' ? '⚙️' : '🔧'}
                      </div>
                      <div className="block-content">
                        <div className="block-name">{block.name}</div>
                        <div className="block-type">{block.type.replace('_', ' ')}</div>
                        <div className="block-confidence">
                          {(block.confidence * 100).toFixed(0)}% confidence
                        </div>
                      </div>
                    </div>
                    {index < result.blocks.length - 1 && (
                      <div className="block-connector">→</div>
                    )}
                  </React.Fragment>
                ))}
              </div>
            </div>

            {/* Configuration Preview */}
            <div className="config-preview">
              <h4>⚙️ Configuration</h4>
              <div className="config-cards">
                <div className="config-card">
                  <div className="config-label">Connections</div>
                  <div className="config-value">{result.connections.length}</div>
                </div>
                <div className="config-card">
                  <div className="config-label">Auto Retry</div>
                  <div className="config-value">
                    {result.workflow_config.settings?.auto_retry ? 'Enabled' : 'Disabled'}
                  </div>
                </div>
                <div className="config-card">
                  <div className="config-label">Timeout</div>
                  <div className="config-value">
                    {result.workflow_config.settings?.timeout || 300}s
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="results-actions">
              <button 
                onClick={handleCreateWorkflow}
                className="btn-primary large"
                disabled={creating}
              >
                {creating ? (
                  <>
                    <span className="loading-spinner small"></span>
                    Creating Workflow...
                  </>
                ) : (
                  <>
                    <span className="btn-icon">🚀</span>
                    Create Workflow
                  </>
                )}
              </button>
              
              <button 
                onClick={() => setResult(null)}
                className="btn-secondary"
              >
                Start Over
              </button>
            </div>
          </div>
        </div>
      )}
    </div>

    {/* Tips Section */}
    <div className="nlp-tips-section">
      <div className="tips-header">
        <h3>💡 Tips for Better Results</h3>
      </div>
      
      <div className="tips-grid">
        <div className="tip-card">
          <div className="tip-icon">🎯</div>
          <h4>Be Specific</h4>
          <p>Include details about data sources, timing, and desired outputs</p>
        </div>
        
        <div className="tip-card">
          <div className="tip-icon">🔗</div>
          <h4>Mention Integrations</h4>
          <p>Specify services like Gmail, Twitter, Slack, or APIs you want to use</p>
        </div>
        
        <div className="tip-card">
          <div className="tip-icon">📋</div>
          <h4>Describe the Flow</h4>
          <p>Explain the step-by-step process from trigger to final action</p>
        </div>
        
        <div className="tip-card">
          <div className="tip-icon">⏰</div>
          <h4>Include Timing</h4>
          <p>Specify when the automation should run (daily, hourly, on trigger)</p>
        </div>
      </div>
    </div>
  </div>
</div>
```

);
};

export default NLPBuilder;