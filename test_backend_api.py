"""
Tests for FastAPI Backend Endpoints
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from backend_main import app
from backend_database import get_db
from backend_models import Base, User
from backend_auth import get_password_hash

# Test database setup
TEST_DATABASE_URL = "sqlite:///:memory:"

engine = create_engine(TEST_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    """Override database dependency for testing"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture
def client():
    """Create test client"""
    Base.metadata.create_all(bind=engine)
    with TestClient(app) as c:
        yield c
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def test_user():
    """Create a test user in database"""
    db = TestingSessionLocal()
    user = User(
        email="<EMAIL>",
        name="Test User",
        hashed_password=get_password_hash("testpassword123")
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    db.close()
    return user

class TestHealthEndpoint:
    """Test health check endpoint"""
    
    def test_health_check(self, client):
        """Test health endpoint returns 200"""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data
        assert "services" in data

class TestAuthEndpoints:
    """Test authentication endpoints"""
    
    def test_register_user(self, client):
        """Test user registration"""
        user_data = {
            "email": "<EMAIL>",
            "name": "New User",
            "password": "password123"
        }
        
        response = client.post("/auth/register", json=user_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["email"] == "<EMAIL>"
        assert data["name"] == "New User"
        assert data["is_active"] is True
        assert "id" in data
        
    def test_register_duplicate_email(self, client, test_user):
        """Test registration with duplicate email fails"""
        user_data = {
            "email": "<EMAIL>",  # Same as test_user
            "name": "Another User",
            "password": "password123"
        }
        
        response = client.post("/auth/register", json=user_data)
        assert response.status_code == 400
        
    def test_login_success(self, client, test_user):
        """Test successful login"""
        login_data = {
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        
        response = client.post("/auth/login", json=login_data)
        assert response.status_code == 200
        
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        
    def test_login_invalid_credentials(self, client, test_user):
        """Test login with invalid credentials"""
        login_data = {
            "email": "<EMAIL>",
            "password": "wrongpassword"
        }
        
        response = client.post("/auth/login", json=login_data)
        assert response.status_code == 401
        
    def test_login_nonexistent_user(self, client):
        """Test login with nonexistent user"""
        login_data = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        response = client.post("/auth/login", json=login_data)
        assert response.status_code == 401

class TestProtectedEndpoints:
    """Test endpoints that require authentication"""
    
    def get_auth_headers(self, client, test_user):
        """Helper to get authentication headers"""
        login_data = {
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        
        response = client.post("/auth/login", json=login_data)
        token = response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    
    def test_get_current_user(self, client, test_user):
        """Test getting current user info"""
        headers = self.get_auth_headers(client, test_user)
        
        response = client.get("/auth/me", headers=headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["email"] == "<EMAIL>"
        assert data["name"] == "Test User"
        
    def test_get_workflows_empty(self, client, test_user):
        """Test getting workflows when user has none"""
        headers = self.get_auth_headers(client, test_user)
        
        response = client.get("/workflows", headers=headers)
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 0
        
    def test_create_workflow(self, client, test_user):
        """Test creating a new workflow"""
        headers = self.get_auth_headers(client, test_user)
        
        workflow_data = {
            "name": "Test Workflow",
            "description": "A test workflow",
            "config": {"blocks": [], "connections": []}
        }
        
        response = client.post("/workflows", json=workflow_data, headers=headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["name"] == "Test Workflow"
        assert data["description"] == "A test workflow"
        assert "id" in data
        
    def test_unauthorized_access(self, client):
        """Test accessing protected endpoint without auth"""
        response = client.get("/workflows")
        assert response.status_code == 401

class TestNLPEndpoints:
    """Test NLP processing endpoints"""
    
    def test_process_nlp_unauthorized(self, client):
        """Test NLP processing without authentication"""
        nlp_data = {
            "description": "Create a workflow that sends emails"
        }
        
        response = client.post("/nlp/process", json=nlp_data)
        assert response.status_code == 401
        
    def test_process_nlp_authorized(self, client, test_user):
        """Test NLP processing with authentication"""
        headers = TestProtectedEndpoints().get_auth_headers(client, test_user)
        
        nlp_data = {
            "description": "Create a workflow that sends emails"
        }
        
        response = client.post("/nlp/process", json=nlp_data, headers=headers)
        # This might fail if Cerebras API key is not set, but should not be 401
        assert response.status_code != 401

class TestAIEndpoints:
    """Test AI model endpoints"""
    
    def test_get_available_models(self, client):
        """Test getting available AI models"""
        response = client.get("/ai/models")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, dict)
        # Should contain model information

if __name__ == "__main__":
    pytest.main([__file__])
