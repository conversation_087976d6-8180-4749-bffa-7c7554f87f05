/* frontend/src/pages/Landing.css */

.landing {
padding-top: 0;
overflow-x: hidden;
}

/* Hero Section */
.hero {
background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
padding: 4rem 0 2rem;
position: relative;
}

.hero::before {
content: ‘’;
position: absolute;
top: 0;
left: 0;
right: 0;
bottom: 0;
background: url(‘data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e2e8f0" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>’);
opacity: 0.5;
z-index: 0;
}

.hero-container {
max-width: 1400px;
margin: 0 auto;
padding: 0 1rem;
display: grid;
grid-template-columns: 1fr 1fr;
gap: 4rem;
align-items: center;
position: relative;
z-index: 1;
}

.hero-content {
animation: slideUp 0.8s ease-out;
}

.hero-badge {
display: inline-flex;
align-items: center;
gap: 0.5rem;
padding: 0.5rem 1rem;
background-color: var(–primary-50);
border: 1px solid var(–primary-200);
border-radius: 2rem;
font-size: 0.875rem;
font-weight: 500;
color: var(–primary-700);
margin-bottom: 2rem;
}

.badge-icon {
font-size: 1rem;
}

.hero-title {
font-size: 3.5rem;
font-weight: 800;
line-height: 1.1;
margin-bottom: 1.5rem;
color: var(–gray-900);
}

.hero-subtitle {
font-size: 1.25rem;
color: var(–gray-600);
margin-bottom: 2rem;
line-height: 1.6;
}

.hero-actions {
display: flex;
gap: 1rem;
margin-bottom: 3rem;
}

.hero-stats {
display: grid;
grid-template-columns: repeat(3, 1fr);
gap: 2rem;
}

.stat {
text-align: center;
}

.stat-number {
font-size: 2rem;
font-weight: 700;
color: var(–primary-600);
margin-bottom: 0.25rem;
}

.stat-label {
font-size: 0.875rem;
color: var(–gray-500);
font-weight: 500;
}

/* Hero Visual */
.hero-visual {
display: flex;
justify-content: center;
align-items: center;
animation: slideUp 0.8s ease-out 0.3s both;
}

.demo-card {
background: white;
border-radius: var(–radius-xl);
padding: 2rem;
box-shadow: var(–shadow-xl);
border: 1px solid var(–gray-200);
max-width: 500px;
position: relative;
}

.demo-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 1.5rem;
}

.demo-title {
font-weight: 600;
color: var(–gray-900);
}

.demo-status {
background-color: var(–success-500);
color: white;
padding: 0.25rem 0.75rem;
border-radius: 1rem;
font-size: 0.75rem;
font-weight: 500;
}

.demo-input {
margin-bottom: 1rem;
}

.input-label {
font-size: 0.875rem;
font-weight: 500;
color: var(–gray-700);
margin-bottom: 0.5rem;
}

.input-text {
background-color: var(–gray-50);
border: 1px solid var(–gray-200);
border-radius: var(–radius-md);
padding: 1rem;
font-size: 0.875rem;
color: var(–gray-600);
line-height: 1.5;
}

.demo-arrow {
text-align: center;
font-size: 1.5rem;
color: var(–primary-500);
margin: 1rem 0;
animation: bounce 2s infinite;
}

@keyframes bounce {
0%, 20%, 50%, 80%, 100% {
transform: translateY(0);
}
40% {
transform: translateY(-10px);
}
60% {
transform: translateY(-5px);
}
}

.demo-output {
background-color: var(–primary-50);
border: 1px solid var(–primary-200);
border-radius: var(–radius-md);
padding: 1rem;
}

.workflow-blocks {
display: flex;
align-items: center;
gap: 0.5rem;
margin-bottom: 0.75rem;
flex-wrap: wrap;
}

.block {
background-color: var(–primary-600);
color: white;
padding: 0.5rem 0.75rem;
border-radius: var(–radius-md);
font-size: 0.75rem;
font-weight: 500;
white-space: nowrap;
}

.connector {
color: var(–primary-600);
font-weight: 600;
}

.confidence {
font-size: 0.75rem;
color: var(–success-500);
font-weight: 600;
}

/* Features Section */
.features {
padding: 5rem 0;
background-color: white;
}

.features-container {
max-width: 1200px;
margin: 0 auto;
padding: 0 1rem;
}

.section-header {
text-align: center;
margin-bottom: 4rem;
}

.section-header h2 {
font-size: 2.5rem;
font-weight: 700;
margin-bottom: 1rem;
color: var(–gray-900);
}

.section-header p {
font-size: 1.125rem;
color: var(–gray-600);
}

.features-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
gap: 2rem;
}

.feature-card {
background: white;
border: 1px solid var(–gray-200);
border-radius: var(–radius-xl);
padding: 2rem;
text-align: center;
transition: all var(–transition-normal);
box-shadow: var(–shadow-sm);
}

.feature-card:hover {
transform: translateY(-4px);
box-shadow: var(–shadow-lg);
border-color: var(–primary-200);
}

.feature-icon {
font-size: 3rem;
margin-bottom: 1rem;
display: block;
}

.feature-card h3 {
font-size: 1.25rem;
font-weight: 600;
margin-bottom: 1rem;
color: var(–gray-900);
}

.feature-card p {
color: var(–gray-600);
line-height: 1.6;
}

/* Use Cases Section */
.use-cases {
padding: 5rem 0;
background: linear-gradient(135deg, var(–primary-50) 0%, var(–gray-50) 100%);
}

.use-cases-container {
max-width: 1200px;
margin: 0 auto;
padding: 0 1rem;
}

.use-cases-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
gap: 2rem;
}

.use-case {
background: white;
border-radius: var(–radius-xl);
padding: 2rem;
border: 1px solid var(–gray-200);
transition: all var(–transition-normal);
}

.use-case:hover {
transform: translateY(-2px);
box-shadow: var(–shadow-lg);
}

.use-case-icon {
font-size: 2.5rem;
margin-bottom: 1rem;
display: block;
}

.use-case h3 {
font-size: 1.25rem;
font-weight: 600;
margin-bottom: 0.75rem;
color: var(–gray-900);
}

.use-case p {
color: var(–gray-600);
line-height: 1.5;
margin-bottom: 1rem;
font-style: italic;
}

.use-case-blocks {
display: flex;
flex-wrap: wrap;
gap: 0.5rem;
}

.block-tag {
background-color: var(–primary-100);
color: var(–primary-700);
padding: 0.25rem 0.5rem;
border-radius: var(–radius-sm);
font-size: 0.75rem;
font-weight: 500;
}

/* Performance Section */
.performance {
padding: 5rem 0;
background-color: var(–gray-900);
color: white;
}

.performance-container {
max-width: 1000px;
margin: 0 auto;
padding: 0 1rem;
}

.performance-content h2 {
text-align: center;
font-size: 2.5rem;
font-weight: 700;
margin-bottom: 1rem;
}

.performance-content p {
text-align: center;
font-size: 1.125rem;
color: var(–gray-300);
margin-bottom: 3rem;
}

.comparison-table {
background: white;
border-radius: var(–radius-xl);
overflow: hidden;
box-shadow: var(–shadow-xl);
}

.comparison-header,
.comparison-row {
display: grid;
grid-template-columns: 2fr 1fr 1fr;
text-align: center;
}

.comparison-header {
background-color: var(–gray-50);
padding: 1rem;
font-weight: 600;
color: var(–gray-900);
border-bottom: 1px solid var(–gray-200);
}

.comparison-row {
padding: 1rem;
border-bottom: 1px solid var(–gray-100);
color: var(–gray-900);
}

.comparison-row:last-child {
border-bottom: none;
}

.metric {
font-weight: 500;
text-align: left;
padding-left: 1rem;
}

.value {
font-weight: 600;
}

.value.highlight {
color: var(–primary-600);
background-color: var(–primary-50);
border-radius: var(–radius-md);
padding: 0.25rem 0.5rem;
}

/* CTA Section */
.cta {
padding: 5rem 0;
background: linear-gradient(135deg, var(–primary-600) 0%, #8b5cf6 100%);
color: white;
text-align: center;
}

.cta-container {
max-width: 800px;
margin: 0 auto;
padding: 0 1rem;
}

.cta-content h2 {
font-size: 2.5rem;
font-weight: 700;
margin-bottom: 1rem;
}

.cta-content p {
font-size: 1.25rem;
margin-bottom: 2rem;
color: rgba(255, 255, 255, 0.9);
}

.cta-actions {
margin-bottom: 1rem;
}

.cta-note {
font-size: 0.875rem;
color: rgba(255, 255, 255, 0.8);
}

/* Responsive Design */
@media (max-width: 768px) {
.hero-container {
grid-template-columns: 1fr;
gap: 2rem;
text-align: center;
}

.hero-title {
font-size: 2.5rem;
}

.hero-actions {
flex-direction: column;
align-items: center;
}

.features-grid,
.use-cases-grid {
grid-template-columns: 1fr;
}

.comparison-header,
.comparison-row {
grid-template-columns: 1fr;
gap: 0.5rem;
}

.comparison-header > div,
.comparison-row > div {
padding: 0.5rem;
text-align: center;
}

.metric {
text-align: center;
padding-left: 0;
font-weight: 600;
background-color: var(–gray-50);
border-radius: var(–radius-sm);
}
}

@media (max-width: 480px) {
.hero {
padding: 2rem 0 1rem;
}

.hero-title {
font-size: 2rem;
}

.hero-subtitle {
font-size: 1rem;
}

.hero-stats {
grid-template-columns: 1fr;
gap: 1rem;
}

.section-header h2 {
font-size: 2rem;
}

.feature-card,
.use-case {
padding: 1.5rem;
}

.demo-card {
padding: 1.5rem;
}

.workflow-blocks {
flex-direction: column;
align-items: flex-start;
gap: 0.25rem;
}

.connector {
display: none;
}
}