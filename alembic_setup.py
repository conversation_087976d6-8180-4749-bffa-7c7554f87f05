# backend/alembic.ini

[alembic]
script_location = alembic
prepend_sys_path = .
version_path_separator = os
sqlalchemy.url = postgresql://autogpt:autogpt123@localhost:5432/autogpt

[post_write_hooks]

[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S

-----

# backend/alembic/env.py

from logging.config import fileConfig
from sqlalchemy import engine_from_config, pool
from alembic import context
import os
import sys

# Add the backend directory to the Python path

sys.path.append(os.path.dirname(os.path.dirname(**file**)))

from models import Base
from database import DATABASE_URL

config = context.config

# Set the SQLAlchemy URL

config.set_main_option(“sqlalchemy.url”, DATABASE_URL)

if config.config_file_name is not None:
fileConfig(config.config_file_name)

target_metadata = Base.metadata

def run_migrations_offline() -> None:
“”“Run migrations in ‘offline’ mode.”””
url = config.get_main_option(“sqlalchemy.url”)
context.configure(
url=url,
target_metadata=target_metadata,
literal_binds=True,
dialect_opts={“paramstyle”: “named”},
)

```
with context.begin_transaction():
    context.run_migrations()
```

def run_migrations_online() -> None:
“”“Run migrations in ‘online’ mode.”””
connectable = engine_from_config(
config.get_section(config.config_ini_section),
prefix=“sqlalchemy.”,
poolclass=pool.NullPool,
)

```
with connectable.connect() as connection:
    context.configure(
        connection=connection, target_metadata=target_metadata
    )

    with context.begin_transaction():
        context.run_migrations()
```

if context.is_offline_mode():
run_migrations_offline()
else:
run_migrations_online()

-----

# backend/alembic/script.py.mako

“””${message}

Revision ID: ${up_revision}
Revises: ${down_revision | comma,n}
Create Date: ${create_date}

“””
from alembic import op
import sqlalchemy as sa
${imports if imports else “”}

# revision identifiers

revision = ${repr(up_revision)}
down_revision = ${repr(down_revision)}
branch_labels = ${repr(branch_labels)}
depends_on = ${repr(depends_on)}

def upgrade() -> None:
${upgrades if upgrades else “pass”}

def downgrade() -> None:
${downgrades if downgrades else “pass”}