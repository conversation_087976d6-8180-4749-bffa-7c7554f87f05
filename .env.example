# AutoGPT Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# REQUIRED SETTINGS
# =============================================================================

# Cerebras AI API Key (Required)
# Get your API key from: https://inference.cerebras.ai/
CEREBRAS_API_KEY=your_cerebras_api_key_here

# Application Secret Key (Required for JWT tokens)
# Generate a secure random string for production
SECRET_KEY=your-super-secret-key-change-in-production

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Development (SQLite)
DATABASE_URL=sqlite:///./autogpt.db

# Production (PostgreSQL)
# DATABASE_URL=postgresql://username:password@localhost:5432/autogpt_prod

# =============================================================================
# CORS AND SECURITY
# =============================================================================

# Allowed origins for CORS (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# =============================================================================
# OPTIONAL SETTINGS
# =============================================================================

# Redis URL for caching and rate limiting
# REDIS_URL=redis://localhost:6379/0

# Email Configuration (for notifications)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-app-password

# Logging Level
LOG_LEVEL=INFO

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# =============================================================================
# FRONTEND ENVIRONMENT VARIABLES
# =============================================================================

# API Base URL
REACT_APP_API_URL=http://localhost:8000

# WebSocket URL
REACT_APP_WS_URL=ws://localhost:8000

# Analytics (optional)
# REACT_APP_GA_TRACKING_ID=your-google-analytics-id

# =============================================================================
# PRODUCTION OVERRIDES
# =============================================================================

# For production deployment, override these values:
# SECRET_KEY=your-production-secret-key-min-32-chars
# DATABASE_URL=********************************/autogpt_prod
# CORS_ORIGINS=https://yourdomain.com
# REACT_APP_API_URL=https://api.yourdomain.com
# REACT_APP_WS_URL=wss://api.yourdomain.com
