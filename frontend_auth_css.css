/* frontend/src/pages/Auth.css */

.auth-page {
min-height: 100vh;
display: grid;
grid-template-columns: 1fr 1fr;
padding-top: 0;
}

.auth-container {
display: flex;
flex-direction: column;
justify-content: center;
padding: 2rem;
background-color: white;
position: relative;
z-index: 1;
}

.auth-visual {
background: linear-gradient(135deg, var(–primary-600) 0%, #8b5cf6 100%);
display: flex;
align-items: center;
justify-content: center;
padding: 2rem;
color: white;
position: relative;
overflow: hidden;
}

.auth-visual::before {
content: ‘’;
position: absolute;
top: 0;
left: 0;
right: 0;
bottom: 0;
background: url(‘data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>’);
z-index: 0;
}

.visual-content {
max-width: 500px;
position: relative;
z-index: 1;
}

/* Auth Header */
.auth-header {
text-align: center;
margin-bottom: 2rem;
}

.auth-logo {
display: inline-flex;
align-items: center;
gap: 0.5rem;
text-decoration: none;
color: var(–gray-900);
font-weight: 700;
font-size: 1.5rem;
margin-bottom: 2rem;
}

.auth-header h1 {
font-size: 2rem;
font-weight: 700;
margin-bottom: 0.5rem;
color: var(–gray-900);
}

.auth-header p {
color: var(–gray-600);
font-size: 1rem;
}

/* Auth Form */
.auth-form {
max-width: 400px;
margin: 0 auto;
}

.form-options {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 1.5rem;
}

.checkbox-label {
display: flex;
align-items: center;
gap: 0.5rem;
cursor: pointer;
font-size: 0.875rem;
color: var(–gray-600);
}

.checkbox-label input[type=“checkbox”] {
width: auto;
margin: 0;
}

.checkmark {
width: 1rem;
height: 1rem;
border: 1px solid var(–gray-300);
border-radius: var(–radius-sm);
display: flex;
align-items: center;
justify-content: center;
transition: all var(–transition-fast);
}

.checkbox-label input[type=“checkbox”]:checked + .checkmark {
background-color: var(–primary-600);
border-color: var(–primary-600);
}

.checkbox-label input[type=“checkbox”]:checked + .checkmark::after {
content: ‘✓’;
color: white;
font-size: 0.75rem;
font-weight: 600;
}

.forgot-link {
color: var(–primary-600);
text-decoration: none;
font-size: 0.875rem;
font-weight: 500;
}

.forgot-link:hover {
text-decoration: underline;
}

.auth-divider {
position: relative;
text-align: center;
margin: 1.5rem 0;
}

.auth-divider::before {
content: ‘’;
position: absolute;
top: 50%;
left: 0;
right: 0;
height: 1px;
background-color: var(–gray-200);
}

.auth-divider span {
background-color: white;
padding: 0 1rem;
color: var(–gray-500);
font-size: 0.875rem;
}

/* Auth Footer */
.auth-footer {
text-align: center;
margin-top: 2rem;
padding-top: 1.5rem;
border-top: 1px solid var(–gray-100);
}

.auth-footer p {
color: var(–gray-600);
font-size: 0.875rem;
}

.auth-link {
color: var(–primary-600);
text-decoration: none;
font-weight: 500;
}

.auth-link:hover {
text-decoration: underline;
}

/* Auth Features */
.auth-features {
display: flex;
justify-content: center;
gap: 2rem;
margin-top: 2rem;
padding-top: 1.5rem;
border-top: 1px solid var(–gray-100);
}

.feature-item {
display: flex;
align-items: center;
gap: 0.5rem;
font-size: 0.75rem;
color: var(–gray-500);
}

.feature-icon {
font-size: 1rem;
}

/* Password Strength */
.password-strength {
margin-top: 0.5rem;
display: flex;
align-items: center;
gap: 0.75rem;
}

.strength-bar {
flex: 1;
height: 4px;
background-color: var(–gray-200);
border-radius: 2px;
overflow: hidden;
}

.strength-fill {
height: 100%;
transition: width var(–transition-normal);
border-radius: 2px;
}

.strength-label {
font-size: 0.75rem;
font-weight: 500;
min-width: 60px;
}

/* Form Agreement */
.form-agreement {
margin-bottom: 1.5rem;
}

.form-agreement .checkbox-label {
font-size: 0.75rem;
line-height: 1.4;
}

/* Auth Benefits */
.auth-benefits {
margin-top: 2rem;
padding-top: 1.5rem;
border-top: 1px solid var(–gray-100);
}

.auth-benefits h3 {
font-size: 1rem;
font-weight: 600;
margin-bottom: 1rem;
color: var(–gray-900);
}

.benefit-list {
display: grid;
gap: 0.75rem;
}

.benefit-item {
display: flex;
align-items: center;
gap: 0.75rem;
font-size: 0.875rem;
color: var(–gray-600);
}

.benefit-icon {
color: var(–success-500);
font-size: 1rem;
}

/* Visual Content Styling */
.visual-content h2 {
font-size: 2rem;
font-weight: 700;
margin-bottom: 1rem;
line-height: 1.2;
}

.visual-content p {
font-size: 1.125rem;
margin-bottom: 2rem;
color: rgba(255, 255, 255, 0.9);
line-height: 1.6;
}

.feature-showcase {
display: grid;
gap: 1.5rem;
margin-bottom: 2rem;
}

.showcase-item {
display: flex;
align-items: center;
gap: 1rem;
padding: 1rem;
background: rgba(255, 255, 255, 0.1);
border-radius: var(–radius-lg);
backdrop-filter: blur(10px);
}

.showcase-icon {
font-size: 2rem;
opacity: 0.9;
}

.showcase-text strong {
display: block;
font-size: 1.125rem;
font-weight: 600;
margin-bottom: 0.25rem;
}

.showcase-text span {
font-size: 0.875rem;
color: rgba(255, 255, 255, 0.8);
}

/* Demo Flow */
.demo-flow {
display: grid;
gap: 1rem;
margin-bottom: 2rem;
}

.demo-step {
display: flex;
align-items: center;
gap: 1rem;
padding: 1rem;
background: rgba(255, 255, 255, 0.1);
border-radius: var(–radius-lg);
backdrop-filter: blur(10px);
}

.step-number {
width: 2rem;
height: 2rem;
border-radius: 50%;
background-color: white;
color: var(–primary-600);
display: flex;
align-items: center;
justify-content: center;
font-weight: 700;
font-size: 0.875rem;
flex-shrink: 0;
}

.step-content h4 {
font-size: 1rem;
font-weight: 600;
margin-bottom: 0.25rem;
}

.step-content p {
font-size: 0.875rem;
color: rgba(255, 255, 255, 0.8);
margin: 0;
}

.flow-arrow {
text-align: center;
font-size: 1.5rem;
opacity: 0.7;
}

/* Testimonial */
.testimonial {
padding: 1.5rem;
background: rgba(255, 255, 255, 0.1);
border-radius: var(–radius-lg);
backdrop-filter: blur(10px);
text-align: center;
}

.testimonial blockquote {
font-size: 1rem;
font-style: italic;
margin-bottom: 1rem;
line-height: 1.6;
}

.testimonial cite {
font-size: 0.875rem;
color: rgba(255, 255, 255, 0.8);
font-style: normal;
font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
.auth-page {
grid-template-columns: 1fr;
}

.auth-visual {
order: -1;
min-height: 40vh;
}

.auth-container {
padding: 1.5rem;
}

.feature-showcase {
gap: 1rem;
}

.showcase-item {
flex-direction: column;
text-align: center;
gap: 0.75rem;
}

.demo-step {
flex-direction: column;
text-align: center;
gap: 0.75rem;
}
}

@media (max-width: 480px) {
.auth-container {
padding: 1rem;
}

.auth-features {
flex-direction: column;
gap: 1rem;
text-align: center;
}

.visual-content h2 {
font-size: 1.5rem;
}

.visual-content p {
font-size: 1rem;
}

.form-options {
flex-direction: column;
align-items: flex-start;
gap: 1rem;
}
}

/* Loading Spinner in Auth */
.auth-form .loading-spinner {
border-color: transparent;
border-top-color: currentColor;
}

/* Focus States */
.auth-form input:focus,
.auth-form textarea:focus,
.auth-form select:focus {
border-color: var(–primary-500);
box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
.auth-visual {
background: var(–gray-900);
}

.showcase-item,
.demo-step,
.testimonial {
background: rgba(255, 255, 255, 0.2);
border: 1px solid rgba(255, 255, 255, 0.3);
}
}