# Frontend Dockerfile
FROM node:18-alpine as build

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY frontend_*.js ./src/
COPY frontend_*.css ./src/
COPY error_boundary.js ./src/
COPY websocket_service.js ./src/

# Create public directory and basic files
RUN mkdir -p public
RUN echo '<!DOCTYPE html><html><head><title>AutoGPT</title></head><body><div id="root"></div></body></html>' > public/index.html

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built files
COPY --from=build /app/build /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port
EXPOSE 3000

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
