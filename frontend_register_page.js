// frontend/src/pages/Register.js
import React, { useState, useEffect } from ‘react’;
import { Link, useNavigate } from ‘react-router-dom’;
import { useAuth } from ‘../contexts/AuthContext’;
import ‘./Auth.css’;

const Register = () => {
const [formData, setFormData] = useState({
name: ‘’,
email: ‘’,
password: ‘’,
confirmPassword: ‘’
});
const [loading, setLoading] = useState(false);
const [errors, setErrors] = useState({});
const [passwordStrength, setPasswordStrength] = useState(0);

const { register, isAuthenticated } = useAuth();
const navigate = useNavigate();

// Redirect if already authenticated
useEffect(() => {
if (isAuthenticated) {
navigate(’/dashboard’, { replace: true });
}
}, [isAuthenticated, navigate]);

const handleChange = (e) => {
const { name, value } = e.target;
setFormData(prev => ({
…prev,
[name]: value
}));

```
// Clear error for this field
if (errors[name]) {
  setErrors(prev => ({
    ...prev,
    [name]: ''
  }));
}

// Update password strength
if (name === 'password') {
  setPasswordStrength(calculatePasswordStrength(value));
}
```

};

const calculatePasswordStrength = (password) => {
let strength = 0;
if (password.length >= 8) strength += 25;
if (/[a-z]/.test(password)) strength += 25;
if (/[A-Z]/.test(password)) strength += 25;
if (/[0-9]/.test(password)) strength += 25;
return strength;
};

const getPasswordStrengthLabel = (strength) => {
if (strength < 25) return ‘Weak’;
if (strength < 50) return ‘Fair’;
if (strength < 75) return ‘Good’;
return ‘Strong’;
};

const getPasswordStrengthColor = (strength) => {
if (strength < 25) return ‘#ef4444’;
if (strength < 50) return ‘#f59e0b’;
if (strength < 75) return ‘#3b82f6’;
return ‘#10b981’;
};

const validateForm = () => {
const newErrors = {};

```
if (!formData.name.trim()) {
  newErrors.name = 'Name is required';
} else if (formData.name.trim().length < 2) {
  newErrors.name = 'Name must be at least 2 characters';
}

if (!formData.email) {
  newErrors.email = 'Email is required';
} else if (!/\S+@\S+\.\S+/.test(formData.email)) {
  newErrors.email = 'Please enter a valid email';
}

if (!formData.password) {
  newErrors.password = 'Password is required';
} else if (formData.password.length < 8) {
  newErrors.password = 'Password must be at least 8 characters';
}

if (!formData.confirmPassword) {
  newErrors.confirmPassword = 'Please confirm your password';
} else if (formData.password !== formData.confirmPassword) {
  newErrors.confirmPassword = 'Passwords do not match';
}

setErrors(newErrors);
return Object.keys(newErrors).length === 0;
```

};

const handleSubmit = async (e) => {
e.preventDefault();

```
if (!validateForm()) {
  return;
}

setLoading(true);

try {
  const result = await register({
    name: formData.name.trim(),
    email: formData.email,
    password: formData.password
  });
  
  if (result.success) {
    navigate('/dashboard', { replace: true });
  } else {
    setErrors({ submit: result.error });
  }
} catch (error) {
  setErrors({ submit: 'An unexpected error occurred' });
} finally {
  setLoading(false);
}
```

};

return (
<div className="auth-page">
<div className="auth-container">
<div className="auth-header">
<Link to="/" className="auth-logo">
<span className="logo-icon">🤖</span>
<span className="logo-text">AutoGPT</span>
</Link>
<h1>Create your account</h1>
<p>Start building AI automations in seconds</p>
</div>

```
    <form onSubmit={handleSubmit} className="auth-form">
      {errors.submit && (
        <div className="error-banner">
          {errors.submit}
        </div>
      )}

      <div className="form-group">
        <label htmlFor="name">Full name</label>
        <input
          type="text"
          id="name"
          name="name"
          value={formData.name}
          onChange={handleChange}
          placeholder="Enter your full name"
          className={errors.name ? 'error' : ''}
          disabled={loading}
        />
        {errors.name && <span className="error-text">{errors.name}</span>}
      </div>

      <div className="form-group">
        <label htmlFor="email">Email address</label>
        <input
          type="email"
          id="email"
          name="email"
          value={formData.email}
          onChange={handleChange}
          placeholder="Enter your email"
          className={errors.email ? 'error' : ''}
          disabled={loading}
        />
        {errors.email && <span className="error-text">{errors.email}</span>}
      </div>

      <div className="form-group">
        <label htmlFor="password">Password</label>
        <input
          type="password"
          id="password"
          name="password"
          value={formData.password}
          onChange={handleChange}
          placeholder="Create a password"
          className={errors.password ? 'error' : ''}
          disabled={loading}
        />
        {formData.password && (
          <div className="password-strength">
            <div className="strength-bar">
              <div 
                className="strength-fill"
                style={{
                  width: `${passwordStrength}%`,
                  backgroundColor: getPasswordStrengthColor(passwordStrength)
                }}
              ></div>
            </div>
            <span 
              className="strength-label"
              style={{ color: getPasswordStrengthColor(passwordStrength) }}
            >
              {getPasswordStrengthLabel(passwordStrength)}
            </span>
          </div>
        )}
        {errors.password && <span className="error-text">{errors.password}</span>}
      </div>

      <div className="form-group">
        <label htmlFor="confirmPassword">Confirm password</label>
        <input
          type="password"
          id="confirmPassword"
          name="confirmPassword"
          value={formData.confirmPassword}
          onChange={handleChange}
          placeholder="Confirm your password"
          className={errors.confirmPassword ? 'error' : ''}
          disabled={loading}
        />
        {errors.confirmPassword && <span className="error-text">{errors.confirmPassword}</span>}
      </div>

      <div className="form-agreement">
        <label className="checkbox-label">
          <input type="checkbox" required />
          <span className="checkmark"></span>
          I agree to the{' '}
          <Link to="/terms" className="auth-link">Terms of Service</Link>
          {' '}and{' '}
          <Link to="/privacy" className="auth-link">Privacy Policy</Link>
        </label>
      </div>

      <button 
        type="submit" 
        className="btn-primary full-width"
        disabled={loading}
      >
        {loading ? (
          <>
            <span className="loading-spinner small"></span>
            Creating account...
          </>
        ) : (
          'Create Account'
        )}
      </button>
    </form>

    <div className="auth-footer">
      <p>
        Already have an account?{' '}
        <Link to="/login" className="auth-link">
          Sign in
        </Link>
      </p>
    </div>

    <div className="auth-benefits">
      <h3>What you get with AutoGPT:</h3>
      <div className="benefit-list">
        <div className="benefit-item">
          <span className="benefit-icon">✅</span>
          <span>Unlimited workflow creation</span>
        </div>
        <div className="benefit-item">
          <span className="benefit-icon">⚡</span>
          <span>Ultra-fast Cerebras AI (2,600+ tokens/sec)</span>
        </div>
        <div className="benefit-item">
          <span className="benefit-icon">🧠</span>
          <span>Advanced NLP processing</span>
        </div>
        <div className="benefit-item">
          <span className="benefit-icon">🔗</span>
          <span>13+ automation blocks</span>
        </div>
        <div className="benefit-item">
          <span className="benefit-icon">📊</span>
          <span>Real-time monitoring & analytics</span>
        </div>
        <div className="benefit-item">
          <span className="benefit-icon">🚀</span>
          <span>One-click deployment</span>
        </div>
      </div>
    </div>
  </div>

  <div className="auth-visual">
    <div className="visual-content">
      <h2>From Description to Automation</h2>
      <p>Watch how AutoGPT transforms natural language into working automations</p>
      
      <div className="demo-flow">
        <div className="demo-step">
          <div className="step-number">1</div>
          <div className="step-content">
            <h4>Describe in English</h4>
            <p>"Send daily reports to my team with sales data analysis"</p>
          </div>
        </div>
        
        <div className="flow-arrow">↓</div>
        
        <div className="demo-step">
          <div className="step-number">2</div>
          <div className="step-content">
            <h4>AI Generates Workflow</h4>
            <p>NLP engine creates optimized automation blocks</p>
          </div>
        </div>
        
        <div className="flow-arrow">↓</div>
        
        <div className="demo-step">
          <div className="step-number">3</div>
          <div className="step-content">
            <h4>Deploy & Monitor</h4>
            <p>One-click deployment with real-time monitoring</p>
          </div>
        </div>
      </div>

      <div className="testimonial">
        <blockquote>
          "AutoGPT cut our automation setup time from hours to minutes. 
          The NLP engine understands exactly what we need."
        </blockquote>
        <cite>Sarah Chen, Head of Operations</cite>
      </div>
    </div>
  </div>
</div>
```

);
};

export default Register;