import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useWorkflow } from './frontend_workflow_context_fixed';

const NLPBuilder = () => {
  const [description, setDescription] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');

  const { processNLP, createWorkflow } = useWorkflow();
  const navigate = useNavigate();

  const examples = [
    {
      title: "Email Auto-Responder",
      description: "Create an agent that monitors my Gmail for customer support emails, generates helpful responses using AI, and sends them back automatically."
    },
    {
      title: "Social Media Manager",
      description: "Build a workflow that checks trending topics on Reddit daily, creates engaging posts about them, and schedules them for Twitter and LinkedIn."
    },
    {
      title: "Business Reporting",
      description: "Make an automation that reads sales data every morning, generates a report with insights, and emails it to my team at 9 AM."
    }
  ];

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!description.trim()) return;

    setIsProcessing(true);
    setError('');
    setResult(null);

    try {
      const response = await processNLP(description);
      
      if (response.success) {
        setResult(response.result);
      } else {
        setError(response.error || 'Failed to process description');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCreateWorkflow = async () => {
    if (!result) return;

    try {
      const workflowData = {
        name: result.suggested_name,
        description: result.suggested_description,
        config: result.workflow_config,
        category: 'nlp_generated'
      };

      const response = await createWorkflow(workflowData);
      
      if (response.success) {
        navigate('/workflows');
      } else {
        setError(response.error || 'Failed to create workflow');
      }
    } catch (err) {
      setError('Failed to create workflow');
    }
  };

  const handleExampleClick = (example) => {
    setDescription(example.description);
    setResult(null);
    setError('');
  };

  return (
    <div className="nlp-builder">
      <div className="page-header">
        <h1>NLP Workflow Builder</h1>
        <p>Describe your automation in natural language and we'll build it for you</p>
      </div>

      <div className="builder-container">
        <div className="builder-input">
          <form onSubmit={handleSubmit} className="nlp-form">
            <div className="form-group">
              <label htmlFor="description">
                Describe what you want to automate:
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="For example: Create an automation that reads my emails, generates AI responses, and sends them automatically..."
                rows={6}
                className="description-input"
                required
              />
            </div>

            <button
              type="submit"
              disabled={isProcessing || !description.trim()}
              className="btn btn-primary btn-large"
            >
              {isProcessing ? 'Processing...' : 'Generate Workflow'}
            </button>
          </form>

          {error && (
            <div className="error-message">
              {error}
            </div>
          )}
        </div>

        {/* Examples */}
        <div className="examples-section">
          <h3>Popular Examples</h3>
          <div className="examples-grid">
            {examples.map((example, index) => (
              <div
                key={index}
                className="example-card"
                onClick={() => handleExampleClick(example)}
              >
                <h4>{example.title}</h4>
                <p>{example.description}</p>
                <span className="example-action">Click to use →</span>
              </div>
            ))}
          </div>
        </div>

        {/* Results */}
        {result && (
          <div className="results-section">
            <div className="results-header">
              <h3>Generated Workflow</h3>
              <div className="confidence-score">
                Confidence: {Math.round(result.confidence * 100)}%
              </div>
            </div>

            <div className="workflow-preview">
              <div className="workflow-info">
                <h4>{result.suggested_name}</h4>
                <p>{result.suggested_description}</p>
              </div>

              <div className="workflow-blocks">
                <h5>Workflow Components:</h5>
                <div className="blocks-flow">
                  {result.blocks.map((block, index) => (
                    <React.Fragment key={index}>
                      <div className="block-preview">
                        <div className="block-icon">
                          {getBlockIcon(block.type)}
                        </div>
                        <div className="block-info">
                          <span className="block-name">{block.name}</span>
                          <span className="block-type">{formatBlockType(block.type)}</span>
                        </div>
                      </div>
                      {index < result.blocks.length - 1 && (
                        <div className="flow-arrow">→</div>
                      )}
                    </React.Fragment>
                  ))}
                </div>
              </div>

              <div className="workflow-config">
                <h5>Configuration Preview:</h5>
                <div className="config-grid">
                  {result.blocks.map((block, index) => (
                    <div key={index} className="config-item">
                      <strong>{block.name}:</strong>
                      <ul>
                        {Object.entries(block.config).slice(0, 3).map(([key, value]) => (
                          <li key={key}>
                            {key}: {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </div>

              <div className="results-actions">
                <button
                  onClick={handleCreateWorkflow}
                  className="btn btn-primary"
                >
                  Create This Workflow
                </button>
                <button
                  onClick={() => setResult(null)}
                  className="btn btn-secondary"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Processing State */}
        {isProcessing && (
          <div className="processing-state">
            <div className="processing-animation">
              <div className="spinner"></div>
              <h3>Analyzing your description...</h3>
              <p>Our AI is understanding your requirements and building the perfect workflow</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Helper functions
const getBlockIcon = (type) => {
  const icons = {
    email_reader: '📧',
    email_sender: '📤',
    ai_text_generator: '🤖',
    web_scraper: '🌐',
    social_poster: '📱',
    scheduler: '⏰',
    conditional: '🔀',
    file_reader: '📁',
    file_writer: '💾',
    notification_sender: '🔔',
    webhook: '🔗',
    api_call: '🔌',
    data_processor: '⚙️'
  };
  return icons[type] || '🔧';
};

const formatBlockType = (type) => {
  return type
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

export default NLPBuilder;
