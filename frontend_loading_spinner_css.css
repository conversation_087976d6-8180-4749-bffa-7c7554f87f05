/* frontend/src/components/LoadingSpinner.css */

.loading-container {
display: flex;
flex-direction: column;
align-items: center;
justify-content: center;
padding: 3rem 2rem;
text-align: center;
}

.loading-spinner {
position: relative;
display: inline-block;
}

/* Default medium size */
.loading-spinner.medium .spinner-ring {
width: 3rem;
height: 3rem;
}

.loading-spinner.small .spinner-ring {
width: 2rem;
height: 2rem;
}

.loading-spinner.large .spinner-ring {
width: 4rem;
height: 4rem;
}

.spinner-ring {
display: inline-block;
position: relative;
}

.spinner-ring div {
box-sizing: border-box;
display: block;
position: absolute;
border: 3px solid var(–primary-500);
border-radius: 50%;
animation: spinner 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
border-color: var(–primary-500) transparent transparent transparent;
}

.spinner-ring div:nth-child(1) {
animation-delay: -0.45s;
}

.spinner-ring div:nth-child(2) {
animation-delay: -0.3s;
}

.spinner-ring div:nth-child(3) {
animation-delay: -0.15s;
}

/* Medium size (default) */
.loading-spinner.medium .spinner-ring div {
width: 3rem;
height: 3rem;
margin: 3px;
}

/* Small size */
.loading-spinner.small .spinner-ring div {
width: 2rem;
height: 2rem;
margin: 2px;
border-width: 2px;
}

/* Large size */
.loading-spinner.large .spinner-ring div {
width: 4rem;
height: 4rem;
margin: 4px;
border-width: 4px;
}

@keyframes spinner {
0% {
transform: rotate(0deg);
}
100% {
transform: rotate(360deg);
}
}

.loading-message {
margin-top: 1rem;
color: var(–gray-600);
font-size: 0.875rem;
font-weight: 500;
}

/* Inline spinner for buttons */
.btn-primary .loading-spinner.small,
.btn-secondary .loading-spinner.small,
.btn-outline .loading-spinner.small {
margin-right: 0.5rem;
}

.btn-primary .loading-spinner.small .spinner-ring div,
.btn-secondary .loading-spinner.small .spinner-ring div,
.btn-outline .loading-spinner.small .spinner-ring div {
border-color: currentColor transparent transparent transparent;
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
.loading-message {
color: var(–gray-300);
}
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
.spinner-ring div {
animation: none;
border: 3px solid var(–primary-500);
border-top-color: transparent;
}

.loading-spinner.small .spinner-ring div {
border-width: 2px;
}

.loading-spinner.large .spinner-ring div {
border-width: 4px;
}
}

/* High contrast mode */
@media (prefers-contrast: high) {
.spinner-ring div {
border-color: var(–primary-700) transparent transparent transparent;
border-width: 4px;
}

.loading-spinner.small .spinner-ring div {
border-width: 3px;
}

.loading-spinner.large .spinner-ring div {
border-width: 5px;
}
}

/* Loading overlay variant */
.loading-overlay {
position: fixed;
top: 0;
left: 0;
right: 0;
bottom: 0;
background: rgba(255, 255, 255, 0.9);
backdrop-filter: blur(2px);
z-index: 9999;
display: flex;
align-items: center;
justify-content: center;
}

.loading-overlay .loading-container {
background: white;
border-radius: var(–radius-lg);
box-shadow: var(–shadow-xl);
border: 1px solid var(–gray-200);
min-width: 200px;
}

/* Pulsing variant */
.loading-spinner.pulse .spinner-ring div {
animation: pulse 1.5s ease-in-out infinite;
border: none;
background: var(–primary-500);
border-radius: 50%;
}

@keyframes pulse {
0% {
transform: scale(0);
opacity: 1;
}
100% {
transform: scale(1);
opacity: 0;
}
}

.loading-spinner.pulse .spinner-ring div:nth-child(2) {
animation-delay: -0.5s;
}

.loading-spinner.pulse .spinner-ring div:nth-child(3) {
animation-delay: -1s;
}

.loading-spinner.pulse .spinner-ring div:nth-child(4) {
animation-delay: -1.5s;
}

/* Dots variant */
.loading-spinner.dots {
display: flex;
gap: 0.25rem;
}

.loading-spinner.dots .spinner-ring {
display: flex;
gap: 0.25rem;
}

.loading-spinner.dots .spinner-ring div {
position: static;
width: 0.5rem;
height: 0.5rem;
background: var(–primary-500);
border: none;
border-radius: 50%;
animation: dots 1.4s ease-in-out infinite both;
}

.loading-spinner.dots .spinner-ring div:nth-child(1) {
animation-delay: -0.32s;
}

.loading-spinner.dots .spinner-ring div:nth-child(2) {
animation-delay: -0.16s;
}

@keyframes dots {
0%, 80%, 100% {
transform: scale(0);
}
40% {
transform: scale(1);
}
}

/* Success state */
.loading-spinner.success .spinner-ring div {
border-color: var(–success-500) transparent transparent transparent;
}

.loading-spinner.success .loading-message {
color: var(–success-600);
}

/* Error state */
.loading-spinner.error .spinner-ring div {
border-color: var(–error-500) transparent transparent transparent;
animation: none;
}

.loading-spinner.error .loading-message {
color: var(–error-600);
}

/* Warning state */
.loading-spinner.warning .spinner-ring div {
border-color: var(–warning-500) transparent transparent transparent;
}

.loading-spinner.warning .loading-message {
color: var(–warning-600);
}