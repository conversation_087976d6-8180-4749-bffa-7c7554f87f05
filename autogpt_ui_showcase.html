<!DOCTYPE html>

<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoGPT UI Showcase</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

```
    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        background-color: #f8fafc;
        color: #1e293b;
        line-height: 1.6;
    }

    /* Navigation */
    .showcase-nav {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: white;
        border-bottom: 1px solid #e2e8f0;
        z-index: 1000;
        padding: 1rem;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .nav-container {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .nav-logo {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 700;
        font-size: 1.25rem;
    }

    .logo-icon {
        font-size: 1.5rem;
    }

    .logo-text {
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .nav-tabs {
        display: flex;
        gap: 0.5rem;
    }

    .nav-tab {
        padding: 0.5rem 1rem;
        border: none;
        background: none;
        color: #64748b;
        cursor: pointer;
        border-radius: 0.5rem;
        transition: all 0.2s;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .nav-tab:hover {
        background: #f1f5f9;
        color: #3b82f6;
    }

    .nav-tab.active {
        background: #3b82f6;
        color: white;
    }

    /* Page Container */
    .page-container {
        margin-top: 80px;
        min-height: calc(100vh - 80px);
    }

    .page {
        display: none;
    }

    .page.active {
        display: block;
    }

    /* Landing Page */
    .landing-hero {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        padding: 4rem 1rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .landing-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e2e8f0" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.5;
    }

    .hero-content {
        max-width: 800px;
        margin: 0 auto;
        position: relative;
        z-index: 1;
    }

    .hero-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background: #eff6ff;
        border: 1px solid #bfdbfe;
        border-radius: 2rem;
        font-size: 0.875rem;
        color: #1d4ed8;
        margin-bottom: 2rem;
    }

    .hero-title {
        font-size: 3rem;
        font-weight: 800;
        margin-bottom: 1rem;
        color: #1e293b;
    }

    .gradient-text {
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .hero-subtitle {
        font-size: 1.25rem;
        color: #64748b;
        margin-bottom: 2rem;
    }

    .hero-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-bottom: 3rem;
    }

    .btn-primary {
        background: #3b82f6;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        border: none;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary:hover {
        background: #2563eb;
        transform: translateY(-1px);
    }

    .btn-secondary {
        background: white;
        color: #374151;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        border: 1px solid #d1d5db;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
    }

    .btn-secondary:hover {
        background: #f9fafb;
    }

    .hero-stats {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
        max-width: 600px;
        margin: 0 auto;
    }

    .stat {
        text-align: center;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #3b82f6;
    }

    .stat-label {
        font-size: 0.875rem;
        color: #6b7280;
    }

    /* Dashboard */
    .dashboard {
        padding: 2rem 1rem;
        max-width: 1400px;
        margin: 0 auto;
    }

    .dashboard-header {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        color: white;
        padding: 2rem;
        border-radius: 1rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    }

    .dashboard-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        position: relative;
        z-index: 1;
    }

    .dashboard-header p {
        font-size: 1.125rem;
        opacity: 0.9;
        position: relative;
        z-index: 1;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 3rem;
    }

    .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 1rem;
        border: 1px solid #e2e8f0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        gap: 1rem;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    }

    .stat-icon {
        font-size: 2.5rem;
        opacity: 0.8;
    }

    .stat-content .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 0.25rem;
    }

    .stat-content .stat-label {
        font-size: 0.875rem;
        color: #64748b;
        font-weight: 500;
    }

    /* NLP Builder */
    .nlp-builder {
        padding: 2rem 1rem;
        max-width: 1200px;
        margin: 0 auto;
    }

    .nlp-header {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        color: white;
        padding: 3rem 2rem;
        border-radius: 1rem;
        text-align: center;
        margin-bottom: 3rem;
    }

    .nlp-header h1 {
        font-size: 3rem;
        font-weight: 800;
        margin-bottom: 1rem;
    }

    .nlp-header p {
        font-size: 1.25rem;
        opacity: 0.95;
        margin-bottom: 2rem;
    }

    .nlp-features {
        display: flex;
        justify-content: center;
        gap: 2rem;
        flex-wrap: wrap;
    }

    .feature-badge {
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 0.75rem 1.5rem;
        border-radius: 2rem;
        backdrop-filter: blur(10px);
    }

    .nlp-input-section {
        background: white;
        padding: 2rem;
        border-radius: 1rem;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .input-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .input-header h2 {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .description-input {
        width: 100%;
        min-height: 150px;
        padding: 1.5rem;
        border: 2px solid #e2e8f0;
        border-radius: 0.75rem;
        font-size: 1rem;
        resize: vertical;
        font-family: inherit;
    }

    .description-input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .input-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #f1f5f9;
    }

    .char-count {
        font-size: 0.875rem;
        color: #6b7280;
    }

    /* Results */
    .nlp-results {
        background: white;
        padding: 2rem;
        border-radius: 1rem;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        margin-top: 2rem;
    }

    .results-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e2e8f0;
    }

    .confidence-score {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .confidence-bar {
        width: 100px;
        height: 8px;
        background: #e2e8f0;
        border-radius: 4px;
        overflow: hidden;
    }

    .confidence-fill {
        height: 100%;
        background: linear-gradient(90deg, #10b981, #059669);
        width: 94%;
        transition: width 0.3s;
    }

    .blocks-flow {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex-wrap: wrap;
        padding: 1.5rem;
        background: #f8fafc;
        border-radius: 0.75rem;
        margin: 1rem 0;
    }

    .block-item {
        background: white;
        border: 2px solid #bfdbfe;
        border-radius: 0.75rem;
        padding: 1rem;
        text-align: center;
        min-width: 120px;
    }

    .block-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .block-name {
        font-size: 0.875rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .block-connector {
        font-size: 1.5rem;
        color: #3b82f6;
        font-weight: 600;
    }

    /* Workflows */
    .workflows {
        padding: 2rem 1rem;
        max-width: 1400px;
        margin: 0 auto;
    }

    .workflows-header {
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        color: white;
        padding: 2rem;
        border-radius: 1rem;
        margin-bottom: 2rem;
    }

    .workflows-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;
    }

    .workflow-card {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 1rem;
        padding: 1.5rem;
        transition: all 0.2s;
        cursor: pointer;
    }

    .workflow-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }

    .workflow-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .workflow-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 0.75rem;
        background: #eff6ff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-badge.active {
        background: #10b981;
        color: white;
    }

    .workflow-title {
        font-size: 1.125rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .workflow-description {
        color: #6b7280;
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }

    /* Analytics */
    .analytics {
        padding: 2rem 1rem;
        max-width: 1400px;
        margin: 0 auto;
    }

    .analytics-header {
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        color: white;
        padding: 2rem;
        border-radius: 1rem;
        margin-bottom: 2rem;
    }

    .charts-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-bottom: 3rem;
    }

    .chart-card {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 1rem;
        padding: 1.5rem;
    }

    .chart-container {
        height: 120px;
        display: flex;
        align-items: end;
        padding: 1rem 0;
    }

    .mini-chart {
        display: flex;
        align-items: end;
        gap: 2px;
        width: 100%;
        height: 100%;
    }

    .chart-bar {
        flex: 1;
        background: #3b82f6;
        min-height: 4px;
        border-radius: 2px;
        transition: all 0.2s;
    }

    .chart-bar:hover {
        opacity: 0.8;
        transform: scaleY(1.05);
    }

    /* Settings */
    .settings {
        padding: 2rem 1rem;
        max-width: 1000px;
        margin: 0 auto;
    }

    .settings-grid {
        display: grid;
        gap: 2rem;
    }

    .settings-section {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 1rem;
        padding: 1.5rem;
    }

    .section-header {
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e2e8f0;
    }

    .section-header h2 {
        font-size: 1.25rem;
        font-weight: 600;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-group label {
        display: block;
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #374151;
    }

    .form-group input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        transition: border-color 0.2s;
    }

    .form-group input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    /* Responsive */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2rem;
        }
        
        .hero-stats {
            grid-template-columns: 1fr;
            gap: 1rem;
        }
        
        .nav-tabs {
            flex-wrap: wrap;
        }
        
        .hero-buttons {
            flex-direction: column;
            align-items: center;
        }
        
        .blocks-flow {
            flex-direction: column;
        }
        
        .workflows-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
```

</head>
<body>
    <!-- Navigation -->
    <div class="showcase-nav">
        <div class="nav-container">
            <div class="nav-logo">
                <span class="logo-icon">🤖</span>
                <span class="logo-text">AutoGPT</span>
            </div>
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="showPage('landing')">Landing</button>
                <button class="nav-tab" onclick="showPage('dashboard')">Dashboard</button>
                <button class="nav-tab" onclick="showPage('nlp-builder')">NLP Builder</button>
                <button class="nav-tab" onclick="showPage('workflows')">Workflows</button>
                <button class="nav-tab" onclick="showPage('analytics')">Analytics</button>
                <button class="nav-tab" onclick="showPage('settings')">Settings</button>
            </div>
        </div>
    </div>

```
<!-- Page Container -->
<div class="page-container">
    <!-- Landing Page -->
    <div id="landing" class="page active">
        <div class="landing-hero">
            <div class="hero-content">
                <div class="hero-badge">
                    <span>⚡</span>
                    <span>Powered by Cerebras • 20x Faster AI</span>
                </div>
                
                <h1 class="hero-title">
                    Transform Ideas into 
                    <span class="gradient-text">AI Automations</span>
                </h1>
                
                <p class="hero-subtitle">
                    Describe your automation in plain English and watch AutoGPT build it instantly. 
                    Ultra-fast Cerebras AI delivers 2,600+ tokens/second for real-time workflow generation.
                </p>
                
                <div class="hero-buttons">
                    <button class="btn-primary" onclick="showPage('nlp-builder')">
                        <span>🚀</span>
                        Start Building Free
                    </button>
                    <button class="btn-secondary" onclick="showPage('dashboard')">
                        View Demo
                    </button>
                </div>
                
                <div class="hero-stats">
                    <div class="stat">
                        <div class="stat-number">2,600+</div>
                        <div class="stat-label">Tokens/Second</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">20x</div>
                        <div class="stat-label">Faster than GPU</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">94%</div>
                        <div class="stat-label">NLP Accuracy</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard -->
    <div id="dashboard" class="page">
        <div class="dashboard">
            <div class="dashboard-header">
                <h1>Welcome back, User! 👋</h1>
                <p>Manage your AI automations and monitor their performance</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">🔧</div>
                    <div class="stat-content">
                        <div class="stat-number">12</div>
                        <div class="stat-label">Total Workflows</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">⚡</div>
                    <div class="stat-content">
                        <div class="stat-number">8</div>
                        <div class="stat-label">Active Workflows</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <div class="stat-number">1,247</div>
                        <div class="stat-label">Total Executions</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-content">
                        <div class="stat-number">96.3%</div>
                        <div class="stat-label">Success Rate</div>
                    </div>
                </div>
            </div>

            <div style="background: white; padding: 2rem; border-radius: 1rem; border: 1px solid #e2e8f0;">
                <h3 style="margin-bottom: 1rem;">Recent Activity</h3>
                <div style="display: flex; flex-direction: column; gap: 1rem;">
                    <div style="display: flex; align-items: center; gap: 1rem; padding: 1rem; background: #f8fafc; border-radius: 0.5rem;">
                        <div style="width: 2rem; height: 2rem; background: #eff6ff; border-radius: 0.5rem; display: flex; align-items: center; justify-content: center;">✅</div>
                        <div>
                            <div style="font-weight: 500;">Workflow "Email Auto-Responder" executed successfully</div>
                            <div style="font-size: 0.875rem; color: #6b7280;">2 minutes ago</div>
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; gap: 1rem; padding: 1rem; background: #f8fafc; border-radius: 0.5rem;">
                        <div style="width: 2rem; height: 2rem; background: #eff6ff; border-radius: 0.5rem; display: flex; align-items: center; justify-content: center;">🔧</div>
                        <div>
                            <div style="font-weight: 500;">Created new workflow "Social Media Manager"</div>
                            <div style="font-size: 0.875rem; color: #6b7280;">1 hour ago</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- NLP Builder -->
    <div id="nlp-builder" class="page">
        <div class="nlp-builder">
            <div class="nlp-header">
                <h1>🧠 Natural Language Builder</h1>
                <p>Describe your automation in plain English and watch AutoGPT build it instantly</p>
                
                <div class="nlp-features">
                    <div class="feature-badge">
                        <span>⚡</span>
                        <span>Ultra-fast Cerebras AI</span>
                    </div>
                    <div class="feature-badge">
                        <span>🎯</span>
                        <span>94% accuracy</span>
                    </div>
                    <div class="feature-badge">
                        <span>🔗</span>
                        <span>13+ block types</span>
                    </div>
                </div>
            </div>

            <div class="nlp-input-section">
                <div class="input-header">
                    <h2>Describe Your Automation</h2>
                    <p>Tell us what you want to automate in natural language</p>
                </div>

                <textarea class="description-input" placeholder="Example: Create an agent that monitors my Gmail for customer emails, generates AI responses, and sends them back automatically..." id="nlpInput">Create an agent that monitors my Gmail inbox for customer support emails, generates helpful responses using AI, and automatically sends them back to customers.</textarea>
                
                <div class="input-footer">
                    <div class="char-count">174/2000 characters</div>
                    <div style="display: flex; gap: 1rem;">
                        <button class="btn-secondary" onclick="clearNLPInput()">Clear</button>
                        <button class="btn-primary" onclick="generateWorkflow()">
                            <span>🔄</span>
                            Generate Automation
                        </button>
                    </div>
                </div>
            </div>

            <div class="nlp-results" id="nlpResults" style="display: none;">
                <div class="results-header">
                    <h3>Generated Automation</h3>
                    <div class="confidence-score">
                        <span style="font-size: 0.875rem; color: #6b7280;">Confidence:</span>
                        <div class="confidence-bar">
                            <div class="confidence-fill"></div>
                        </div>
                        <span style="font-weight: 600;">94.2%</span>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; margin-bottom: 2rem;">
                    <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 0.75rem; padding: 1.5rem;">
                        <h4 style="margin-bottom: 0.75rem;">📝 Suggested Name</h4>
                        <p>Customer Support Email Automation</p>
                    </div>
                    <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 0.75rem; padding: 1.5rem;">
                        <h4 style="margin-bottom: 0.75rem;">📄 Description</h4>
                        <p>Automatically process and respond to customer emails using AI-powered responses</p>
                    </div>
                </div>

                <h4 style="margin-bottom: 1rem;">🔗 Automation Blocks (3)</h4>
                <div class="blocks-flow">
                    <div class="block-item">
                        <div class="block-icon">📧</div>
                        <div class="block-name">Email Reader</div>
                        <div style="font-size: 0.75rem; color: #6b7280;">Gmail Integration</div>
                    </div>
                    <div class="block-connector">→</div>
                    <div class="block-item">
                        <div class="block-icon">🤖</div>
                        <div class="block-name">AI Generator</div>
                        <div style="font-size: 0.75rem; color: #6b7280;">Cerebras AI</div>
                    </div>
                    <div class="block-connector">→</div>
                    <div class="block-item">
                        <div class="block-icon">📤</div>
                        <div class="block-name">Email Sender</div>
                        <div style="font-size: 0.75rem; color: #6b7280;">Auto-Reply</div>
                    </div>
                </div>

                <div style="text-align: center; padding-top: 2rem; border-top: 1px solid #e2e8f0;">
                    <button class="btn-primary" style="padding: 1rem 2rem; font-size: 1rem;" onclick="showPage('workflows')">
                        <span>🚀</span>
                        Create Workflow
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Workflows -->
    <div id="workflows" class="page">
        <div class="workflows">
            <div class="workflows-header">
                <h1>🔧 Workflows</h1>
                <p>Manage and monitor your automation workflows</p>
            </div>

            <div class="workflows-grid">
                <div class="workflow-card">
                    <div class="workflow-header">
                        <div class="workflow-icon">📧</div>
                        <div class="status-badge active">Active</div>
                    </div>
                    <div class="workflow-title">Email Auto-Responder</div>
                    <div class="workflow-description">Automatically respond to customer support emails using AI-generated responses</div>
                    <div style="display: flex; justify-content: space-between; font-size: 0.75rem; color: #6b7280;">
                        <span>🔗 3 blocks</span>
                        <span>📅 Updated 2 hours ago</span>
                    </div>
                </div>

                <div class="workflow-card">
                    <div class="workflow-header">
                        <div class="workflow-icon">📱</div>
                        <div class="status-badge active">Active</div>
                    </div>
                    <div class="workflow-title">Social Media Manager</div>
                    <div class="workflow-description">Monitor trends and create engaging social media content automatically</div>
                    <div style="display: flex; justify-content: space-between; font-size: 0.75rem; color: #6b7280;">
                        <span>🔗 4 blocks</span>
                        <span>📅 Updated 1 day ago</span>
                    </div>
                </div>

                <div class="workflow-card">
                    <div class="workflow-header">
                        <div class="workflow-icon">📊</div>
                        <div style="background: #f3f4f6; color: #6b7280;" class="status-badge">Inactive</div>
                    </div>
                    <div class="workflow-title">Daily Report Generator</div>
                    <div class="workflow-description">Generate and send daily business reports with insights</div>
                    <div style="display: flex; justify-content: space-between; font-size: 0.75rem; color: #6b7280;">
                        <span>🔗 5 blocks</span>
                        <span>📅 Updated 3 days ago</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics -->
    <div id="analytics" class="page">
        <div class="analytics">
            <div class="analytics-header">
                <h1>📈 Analytics & Insights</h1>
                <p>Monitor your automation performance and AI usage</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">🔄</div>
                    <div class="stat-content">
                        <div class="stat-number">1,247</div>
                        <div class="stat-label">Total Executions</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-content">
                        <div class="stat-number">96.3%</div>
                        <div class="stat-label">Success Rate</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">⚡</div>
                    <div class="stat-content">
                        <div class="stat-number">2.3s</div>
                        <div class="stat-label">Avg Response Time</div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">💰</div>
                    <div class="stat-content">
                        <div class="stat-number">$35.78</div>
                        <div class="stat-label">Total Cost</div>
                    </div>
                </div>
            </div>

            <div class="charts-grid">
                <div class="chart-card">
                    <h3 style="margin-bottom: 0.5rem;">Execution Trend</h3>
                    <p style="color: #6b7280; font-size: 0.875rem; margin-bottom: 1rem;">Daily workflow executions</p>
                    <div class="chart-container">
                        <div class="mini-chart">
                            <div class="chart-bar" style="height: 60%;"></div>
                            <div class="chart-bar" style="height: 80%;"></div>
                            <div class="chart-bar" style="height: 45%;"></div>
                            <div class="chart-bar" style="height: 90%;"></div>
                            <div class="chart-bar" style="height: 70%;"></div>
                            <div class="chart-bar" style="height: 85%;"></div>
                            <div class="chart-bar" style="height: 95%;"></div>
                        </div>
                    </div>
                </div>
                
                <div class="chart-card">
                    <h3 style="margin-bottom: 0.5rem;">Success Rate</h3>
                    <p style="color: #6b7280; font-size: 0.875rem; margin-bottom: 1rem;">Execution success percentage</p>
                    <div class="chart-container">
                        <div class="mini-chart">
                            <div class="chart-bar" style="height: 95%; background: #10b981;"></div>
                            <div class="chart-bar" style="height: 97%; background: #10b981;"></div>
                            <div class="chart-bar" style="height: 93%; background: #10b981;"></div>
                            <div class="chart-bar" style="height: 96%; background: #10b981;"></div>
                            <div class="chart-bar" style="height: 98%; background: #10b981;"></div>
                            <div class="chart-bar" style="height: 95%; background: #10b981;"></div>
                            <div class="chart-bar" style="height: 97%; background: #10b981;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings -->
    <div id="settings" class="page">
        <div class="settings">
            <h1 style="margin-bottom: 2rem;">⚙️ Settings</h1>
            
            <div class="settings-grid">
                <div class="settings-section">
                    <div class="section-header">
                        <h2>👤 Profile Settings</h2>
                    </div>
                    
                    <div class="form-group">
                        <label>Full Name</label>
                        <input type="text" value="John Doe" />
                    </div>

                    <div class="form-group">
                        <label>Email Address</label>
                        <input type="email" value="<EMAIL>" />
                    </div>

                    <button class="btn-primary">Update Profile</button>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2>🔑 API Keys</h2>
                    </div>
                    
                    <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 0.5rem; padding: 1rem; margin-bottom: 1rem;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 500;">Cerebras Production</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">CEREBRAS</div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 1rem;">
                                <span style="background: #10b981; color: white; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem;">Active</span>
                                <button style="background: none; border: none; cursor: pointer;">🗑️</button>
                            </div>
                        </div>
                    </div>

                    <button class="btn-primary">Add API Key</button>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2>🔔 Notifications</h2>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                        <div>
                            <div style="font-weight: 500;">Workflow Completion</div>
                            <div style="font-size: 0.875rem; color: #6b7280;">Get notified when workflows complete</div>
                        </div>
                        <label style="position: relative; display: inline-block; width: 60px; height: 34px;">
                            <input type="checkbox" checked style="opacity: 0; width: 0; height: 0;" />
                            <span style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #3b82f6; transition: .4s; border-radius: 34px;"></span>
                        </label>
                    </div>

                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div style="font-weight: 500;">Workflow Failures</div>
                            <div style="font-size: 0.875rem; color: #6b7280;">Get notified when workflows fail</div>
                        </div>
                        <label style="position: relative; display: inline-block; width: 60px; height: 34px;">
                            <input type="checkbox" checked style="opacity: 0; width: 0; height: 0;" />
                            <span style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #3b82f6; transition: .4s; border-radius: 34px;"></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function showPage(pageId) {
        // Hide all pages
        const pages = document.querySelectorAll('.page');
        pages.forEach(page => page.classList.remove('active'));
        
        // Show selected page
        document.getElementById(pageId).classList.add('active');
        
        // Update nav tabs
        const tabs = document.querySelectorAll('.nav-tab');
        tabs.forEach(tab => tab.classList.remove('active'));
        event.target.classList.add('active');
    }

    function generateWorkflow() {
        const input = document.getElementById('nlpInput');
        const results = document.getElementById('nlpResults');
        
        if (input.value.trim()) {
            results.style.display = 'block';
            results.scrollIntoView({ behavior: 'smooth' });
        }
    }

    function clearNLPInput() {
        document.getElementById('nlpInput').value = '';
        document.getElementById('nlpResults').style.display = 'none';
    }

    // Update character count
    document.getElementById('nlpInput').addEventListener('input', function() {
        const charCount = this.value.length;
        document.querySelector('.char-count').textContent = `${charCount}/2000 characters`;
    });
</script>
```

</body>
</html>