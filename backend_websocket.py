"""
WebSocket Support for Real-time Communication
File: backend/websocket.py
"""

import json
import logging
from datetime import datetime
from typing import Dict, Set
from fastapi import WebSocket, WebSocketDisconnect

logger = logging.getLogger(__name__)

class ConnectionManager:
    """Manages WebSocket connections for real-time communication"""
    
    def __init__(self):
        self.active_connections: Dict[int, Set[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, user_id: int):
        """Connect a user's websocket"""
        await websocket.accept()
        
        if user_id not in self.active_connections:
            self.active_connections[user_id] = set()
        
        self.active_connections[user_id].add(websocket)
        logger.info(f"User {user_id} connected via WebSocket")
        
        # Send welcome message
        await self.send_personal_message({
            "type": "connected",
            "message": "Connected to AutoGPT",
            "timestamp": datetime.utcnow().isoformat()
        }, websocket)

    def disconnect(self, websocket: WebSocket, user_id: int):
        """Disconnect a user's websocket"""
        if user_id in self.active_connections:
            self.active_connections[user_id].discard(websocket)
            
            # Remove user if no connections left
            if not self.active_connections[user_id]:
                del self.active_connections[user_id]
        
        logger.info(f"User {user_id} disconnected from WebSocket")

    async def send_personal_message(self, message: dict, websocket: WebSocket):
        """Send message to a specific websocket connection"""
        try:
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            logger.error(f"Error sending WebSocket message: {e}")

    async def send_user_message(self, message: dict, user_id: int):
        """Send message to all connections of a specific user"""
        if user_id in self.active_connections:
            disconnected = set()
            for connection in self.active_connections[user_id]:
                try:
                    await connection.send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"Error sending message to user {user_id}: {e}")
                    disconnected.add(connection)
            
            # Clean up disconnected websockets
            for connection in disconnected:
                self.active_connections[user_id].discard(connection)

    async def broadcast_message(self, message: dict):
        """Broadcast message to all connected users"""
        for user_id, connections in self.active_connections.items():
            disconnected = set()
            for connection in connections:
                try:
                    await connection.send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"Error broadcasting message to user {user_id}: {e}")
                    disconnected.add(connection)
            
            # Clean up disconnected websockets
            for connection in disconnected:
                connections.discard(connection)

    def get_user_count(self) -> int:
        """Get total number of connected users"""
        return len(self.active_connections)

    def get_connection_count(self) -> int:
        """Get total number of active connections"""
        return sum(len(connections) for connections in self.active_connections.values())

    async def notify_workflow_status(self, user_id: int, workflow_id: int, status: str, details: dict = None):
        """Send workflow status update to user"""
        message = {
            "type": "workflow_status",
            "workflow_id": workflow_id,
            "status": status,
            "details": details or {},
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.send_user_message(message, user_id)

    async def notify_execution_update(self, user_id: int, execution_id: int, status: str, progress: float = None):
        """Send execution progress update to user"""
        message = {
            "type": "execution_update",
            "execution_id": execution_id,
            "status": status,
            "progress": progress,
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.send_user_message(message, user_id)

# Global connection manager instance
manager = ConnectionManager()
