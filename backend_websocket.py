# backend/websocket_manager.py

from fastapi import WebSocket, WebSocketDisconnect
from typing import Dict, Set
import json
import logging
from datetime import datetime

logger = logging.getLogger(**name**)

class ConnectionManager:
def **init**(self):
self.active_connections: Dict[int, Set[WebSocket]] = {}

```
async def connect(self, websocket: WebSocket, user_id: int):
    await websocket.accept()
    if user_id not in self.active_connections:
        self.active_connections[user_id] = set()
    self.active_connections[user_id].add(websocket)
    logger.info(f"User {user_id} connected via WebSocket")
    
    # Send welcome message
    await self.send_personal_message({
        "type": "connected",
        "message": "Connected to AutoGPT",
        "timestamp": datetime.utcnow().isoformat()
    }, websocket)
    
def disconnect(self, websocket: WebSocket, user_id: int):
    if user_id in self.active_connections:
        self.active_connections[user_id].discard(websocket)
        if not self.active_connections[user_id]:
            del self.active_connections[user_id]
    logger.info(f"User {user_id} disconnected from WebSocket")
    
async def send_personal_message(self, message: dict, websocket: WebSocket):
    try:
        await websocket.send_text(json.dumps(message))
    except Exception as e:
        logger.error(f"Error sending WebSocket message: {e}")
        
async def send_user_message(self, message: dict, user_id: int):
    if user_id in self.active_connections:
        for connection in self.active_connections[user_id].copy():
            try:
                await connection.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to user {user_id}: {e}")
                self.active_connections[user_id].discard(connection)
                
async def broadcast_message(self, message: dict):
    for user_connections in self.active_connections.values():
        for connection in user_connections.copy():
            try:
                await connection.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")
```

manager = ConnectionManager()

-----

# Add to backend/main.py (WebSocket endpoint)

from fastapi import WebSocket, WebSocketDisconnect, Query
from websocket_manager import manager
import jwt

@app.websocket(”/ws”)
async def websocket_endpoint(websocket: WebSocket, token: str = Query(…)):
try:
# Verify JWT token
payload = jwt.decode(token, SECRET_KEY, algorithms=[“HS256”])
user_id = int(payload.get(“sub”))

```
    await manager.connect(websocket, user_id)
    
    try:
        while True:
            # Receive messages from client
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # Handle different message types
            if message.get("type") == "ping":
                await manager.send_personal_message({
                    "type": "pong",
                    "timestamp": datetime.utcnow().isoformat()
                }, websocket)
            elif message.get("type") == "subscribe_workflow":
                workflow_id = message.get("workflow_id")
                # Handle workflow subscription logic
                await manager.send_personal_message({
                    "type": "subscribed",
                    "workflow_id": workflow_id,
                    "message": f"Subscribed to workflow {workflow_id}"
                }, websocket)
                
    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id)
        
except jwt.PyJWTError:
    await websocket.close(code=1008, reason="Invalid token")
except Exception as e:
    logger.error(f"WebSocket error: {e}")
    await websocket.close(code=1011, reason="Internal server error")
```

# Function to send workflow execution updates

async def send_workflow_update(user_id: int, workflow_id: int, status: str, data: dict = None):
message = {
“type”: “workflow_update”,
“workflow_id”: workflow_id,
“status”: status,
“data”: data or {},
“timestamp”: datetime.utcnow().isoformat()
}
await manager.send_user_message(message, user_id)

# Function to send system notifications

async def send_notification(user_id: int, title: str, message: str, type: str = “info”):
notification = {
“type”: “notification”,
“title”: title,
“message”: message,
“notification_type”: type,
“timestamp”: datetime.utcnow().isoformat()
}
await manager.send_user_message(notification, user_id)