import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from './frontend_auth_context_fixed';
import { useWorkflow } from './frontend_workflow_context_fixed';

const Dashboard = () => {
  const { user } = useAuth();
  const { workflows, isLoading } = useWorkflow();
  const [stats, setStats] = useState({
    totalWorkflows: 0,
    activeWorkflows: 0,
    totalExecutions: 0,
    successRate: 0
  });

  useEffect(() => {
    // Calculate stats from workflows
    const totalWorkflows = workflows.length;
    const activeWorkflows = workflows.filter(w => w.is_active).length;
    
    setStats({
      totalWorkflows,
      activeWorkflows,
      totalExecutions: totalWorkflows * 10, // Mock data
      successRate: 94.5 // Mock data
    });
  }, [workflows]);

  const recentWorkflows = workflows.slice(0, 5);

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <div className="header-content">
          <h1>Welcome back, {user?.name}!</h1>
          <p>Here's what's happening with your automations</p>
        </div>
        
        <div className="header-actions">
          <Link to="/nlp-builder" className="btn btn-primary">
            Create New Workflow
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">🔧</div>
          <div className="stat-content">
            <h3>{stats.totalWorkflows}</h3>
            <p>Total Workflows</p>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">⚡</div>
          <div className="stat-content">
            <h3>{stats.activeWorkflows}</h3>
            <p>Active Workflows</p>
          </div>
        </div>
        
        <div className="stat-card">
          <div className="stat-icon">📊</div>
          <div className="stat-content">
            <h3>{stats.totalExecutions}</h3>
            <p>Total Executions</p>
          </div>
        </div>
        
        <div className="stat-card success">
          <div className="stat-icon">✅</div>
          <div className="stat-content">
            <h3>{stats.successRate}%</h3>
            <p>Success Rate</p>
          </div>
        </div>
      </div>

      <div className="dashboard-content">
        {/* Quick Actions */}
        <section className="dashboard-section">
          <h2>Quick Actions</h2>
          <div className="quick-actions">
            <Link to="/nlp-builder" className="action-card">
              <div className="action-icon">🤖</div>
              <h3>NLP Builder</h3>
              <p>Create workflows using natural language</p>
            </Link>
            
            <Link to="/workflows" className="action-card">
              <div className="action-icon">📋</div>
              <h3>View All Workflows</h3>
              <p>Manage and monitor your automations</p>
            </Link>
            
            <Link to="/analytics" className="action-card">
              <div className="action-icon">📈</div>
              <h3>Analytics</h3>
              <p>View performance metrics and insights</p>
            </Link>
          </div>
        </section>

        {/* Recent Workflows */}
        <section className="dashboard-section">
          <div className="section-header">
            <h2>Recent Workflows</h2>
            <Link to="/workflows" className="section-link">
              View All
            </Link>
          </div>
          
          {isLoading ? (
            <div className="loading-state">
              <div className="spinner"></div>
              <p>Loading workflows...</p>
            </div>
          ) : recentWorkflows.length > 0 ? (
            <div className="workflows-list">
              {recentWorkflows.map(workflow => (
                <div key={workflow.id} className="workflow-item">
                  <div className="workflow-info">
                    <h3>{workflow.name}</h3>
                    <p>{workflow.description}</p>
                    <div className="workflow-meta">
                      <span className={`status ${workflow.is_active ? 'active' : 'inactive'}`}>
                        {workflow.is_active ? 'Active' : 'Inactive'}
                      </span>
                      <span className="category">{workflow.category}</span>
                      <span className="date">
                        {new Date(workflow.created_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                  
                  <div className="workflow-actions">
                    <Link to={`/workflows/${workflow.id}`} className="btn btn-small">
                      Edit
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="empty-state">
              <div className="empty-icon">🤖</div>
              <h3>No workflows yet</h3>
              <p>Create your first automation workflow to get started</p>
              <Link to="/nlp-builder" className="btn btn-primary">
                Create Workflow
              </Link>
            </div>
          )}
        </section>

        {/* System Status */}
        <section className="dashboard-section">
          <h2>System Status</h2>
          <div className="status-grid">
            <div className="status-item">
              <div className="status-indicator online"></div>
              <span>Database</span>
              <span className="status-value">Online</span>
            </div>
            
            <div className="status-item">
              <div className="status-indicator online"></div>
              <span>Cerebras AI</span>
              <span className="status-value">Available</span>
            </div>
            
            <div className="status-item">
              <div className="status-indicator online"></div>
              <span>NLP Engine</span>
              <span className="status-value">Ready</span>
            </div>
            
            <div className="status-item">
              <div className="status-indicator online"></div>
              <span>Workflow Engine</span>
              <span className="status-value">Running</span>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default Dashboard;
