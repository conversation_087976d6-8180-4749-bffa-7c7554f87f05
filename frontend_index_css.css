/* frontend/src/index.css */

/* Import fonts */
@import url(‘https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap’);

/* CSS Reset */
*,
*::before,
*::after {
box-sizing: border-box;
margin: 0;
padding: 0;
}

/* Root styles */
html {
line-height: 1.15;
-webkit-text-size-adjust: 100%;
scroll-behavior: smooth;
}

body {
font-family: ‘Inter’, -apple-system, BlinkMacSystemFont, ‘Segoe UI’, ‘Roboto’, ‘Oxygen’,
‘Ubuntu’, ‘Cantarell’, ‘Fira Sans’, ‘Droid Sans’, ‘Helvetica Neue’,
sans-serif;
-webkit-font-smoothing: antialiased;
-moz-osx-font-smoothing: grayscale;
background-color: #f8fafc;
color: #1e293b;
line-height: 1.6;
font-size: 16px;
}

/* Remove default margins and paddings */
h1, h2, h3, h4, h5, h6,
p, blockquote, pre,
dl, dd, ol, ul,
figure, hr,
fieldset, legend {
margin: 0;
padding: 0;
}

/* Remove list styles */
ol, ul {
list-style: none;
}

/* Make images easier to work with */
img,
picture {
max-width: 100%;
display: block;
}

/* Inherit fonts for inputs and buttons */
input, button, textarea, select {
font: inherit;
}

/* Remove default button styles */
button {
background: none;
border: none;
cursor: pointer;
}

/* Links */
a {
color: inherit;
text-decoration: none;
}

a:hover {
text-decoration: underline;
}

/* Code styling */
code {
font-family: ‘Monaco’, ‘Menlo’, ‘Ubuntu Mono’, ‘Consolas’, ‘source-code-pro’, monospace;
background-color: #f1f5f9;
padding: 0.125rem 0.25rem;
border-radius: 0.25rem;
font-size: 0.875em;
}

pre code {
background: none;
padding: 0;
}

pre {
background-color: #1e293b;
color: #e2e8f0;
padding: 1rem;
border-radius: 0.5rem;
overflow-x: auto;
font-size: 0.875rem;
line-height: 1.5;
}

/* Selection styling */
::selection {
background-color: rgba(59, 130, 246, 0.2);
color: inherit;
}

/* Focus styles */
:focus {
outline: 2px solid #3b82f6;
outline-offset: 2px;
}

:focus:not(:focus-visible) {
outline: none;
}

/* Scrollbar styling */
::-webkit-scrollbar {
width: 8px;
height: 8px;
}

::-webkit-scrollbar-track {
background: #f1f5f9;
border-radius: 4px;
}

::-webkit-scrollbar-thumb {
background: #cbd5e1;
border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
background: #94a3b8;
}

/* Support for Firefox scrollbars */
html {
scrollbar-width: thin;
scrollbar-color: #cbd5e1 #f1f5f9;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
*,
*::before,
*::after {
animation-duration: 0.01ms !important;
animation-iteration-count: 1 !important;
transition-duration: 0.01ms !important;
scroll-behavior: auto !important;
}
}

/* High contrast mode support */
@media (prefers-contrast: high) {
body {
background-color: white;
color: black;
}
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
body {
background-color: #0f172a;
color: #f1f5f9;
}

code {
background-color: #334155;
color: #e2e8f0;
}

::-webkit-scrollbar-track {
background: #1e293b;
}

::-webkit-scrollbar-thumb {
background: #475569;
}

::-webkit-scrollbar-thumb:hover {
background: #64748b;
}

html {
scrollbar-color: #475569 #1e293b;
}
}

/* Print styles */
@media print {
body {
background: white !important;
color: black !important;
font-size: 12pt;
line-height: 1.4;
}

a {
text-decoration: underline;
}

pre, code {
background: #f5f5f5 !important;
color: black !important;
}

/* Hide non-essential elements when printing */
.no-print {
display: none !important;
}
}

/* Utility classes */
.sr-only {
position: absolute;
width: 1px;
height: 1px;
padding: 0;
margin: -1px;
overflow: hidden;
clip: rect(0, 0, 0, 0);
white-space: nowrap;
border: 0;
}

.skip-link {
position: absolute;
top: -40px;
left: 6px;
background: #3b82f6;
color: white;
padding: 8px;
text-decoration: none;
border-radius: 4px;
z-index: 10000;
}

.skip-link:focus {
top: 6px;
}

/* React Toastify custom styles */
.Toastify__toast-container {
font-family: inherit;
}

.Toastify__toast {
border-radius: 0.5rem;
box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
font-size: 0.875rem;
}

.Toastify__toast–success {
background: #10b981;
}

.Toastify__toast–error {
background: #ef4444;
}

.Toastify__toast–warning {
background: #f59e0b;
}

.Toastify__toast–info {
background: #3b82f6;
}

.Toastify__progress-bar {
background: rgba(255, 255, 255, 0.7);
}

/* Loading state for the entire app */
#root {
min-height: 100vh;
}

.app-loading {
display: flex;
align-items: center;
justify-content: center;
min-height: 100vh;
background: #f8fafc;
}

.app-loading .loading-spinner {
margin-bottom: 1rem;
}

.app-loading .loading-text {
color: #64748b;
font-weight: 500;
}

/* Error boundary styles */
.error-boundary {
display: flex;
flex-direction: column;
align-items: center;
justify-content: center;
min-height: 100vh;
padding: 2rem;
text-align: center;
background: #f8fafc;
}

.error-boundary h1 {
font-size: 2rem;
font-weight: 700;
color: #1e293b;
margin-bottom: 1rem;
}

.error-boundary p {
color: #64748b;
margin-bottom: 2rem;
max-width: 600px;
}

.error-boundary button {
background: #3b82f6;
color: white;
padding: 0.75rem 1.5rem;
border-radius: 0.5rem;
font-weight: 500;
transition: background-color 0.2s ease;
}

.error-boundary button:hover {
background: #2563eb;
}

/* Ensure consistent font rendering */
body, input, textarea, button, select {
font-feature-settings: ‘kern’ 1, ‘liga’ 1, ‘calt’ 1;
text-rendering: optimizeLegibility;
}

/* Prevent horizontal scrolling */
html, body {
max-width: 100%;
overflow-x: hidden;
}