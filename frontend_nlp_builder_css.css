/* frontend/src/pages/NLPBuilder.css */

.nlp-builder {
padding: 2rem 0;
background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
min-height: calc(100vh - 4rem);
}

.nlp-container {
max-width: 1400px;
margin: 0 auto;
padding: 0 1rem;
}

/* NLP Header */
.nlp-header {
text-align: center;
margin-bottom: 3rem;
padding: 3rem 2rem;
background: linear-gradient(135deg, var(–primary-600) 0%, #8b5cf6 100%);
border-radius: var(–radius-xl);
color: white;
position: relative;
overflow: hidden;
}

.nlp-header::before {
content: ‘’;
position: absolute;
top: 0;
left: 0;
right: 0;
bottom: 0;
background: url(‘data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="nlp-pattern" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23nlp-pattern)"/></svg>’);
z-index: 0;
}

.nlp-header > * {
position: relative;
z-index: 1;
}

.nlp-header h1 {
font-size: 3rem;
font-weight: 800;
margin-bottom: 1rem;
text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.nlp-header p {
font-size: 1.25rem;
color: rgba(255, 255, 255, 0.95);
margin-bottom: 2rem;
max-width: 600px;
margin-left: auto;
margin-right: auto;
line-height: 1.6;
}

.nlp-features {
display: flex;
justify-content: center;
gap: 2rem;
flex-wrap: wrap;
}

.feature-badge {
display: flex;
align-items: center;
gap: 0.5rem;
padding: 0.75rem 1.5rem;
background: rgba(255, 255, 255, 0.15);
border: 1px solid rgba(255, 255, 255, 0.2);
border-radius: 2rem;
font-weight: 500;
backdrop-filter: blur(10px);
transition: all var(–transition-fast);
}

.feature-badge:hover {
background: rgba(255, 255, 255, 0.25);
}

.badge-icon {
font-size: 1.125rem;
}

/* NLP Content */
.nlp-content {
display: grid;
gap: 3rem;
}

/* Input Section */
.nlp-input-section {
background: white;
border-radius: var(–radius-xl);
padding: 2rem;
box-shadow: var(–shadow-lg);
border: 1px solid var(–gray-200);
}

.input-header {
text-align: center;
margin-bottom: 2rem;
}

.input-header h2 {
font-size: 1.75rem;
font-weight: 700;
color: var(–gray-900);
margin-bottom: 0.5rem;
}

.input-header p {
color: var(–gray-600);
font-size: 1rem;
}

.input-container {
position: relative;
}

.description-input {
width: 100%;
min-height: 150px;
padding: 1.5rem;
border: 2px solid var(–gray-200);
border-radius: var(–radius-lg);
font-size: 1rem;
line-height: 1.6;
resize: vertical;
transition: all var(–transition-fast);
font-family: inherit;
}

.description-input:focus {
border-color: var(–primary-500);
box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
outline: none;
}

.description-input::placeholder {
color: var(–gray-400);
font-style: italic;
}

.input-footer {
display: flex;
justify-content: space-between;
align-items: center;
margin-top: 1rem;
padding-top: 1rem;
border-top: 1px solid var(–gray-100);
}

.char-count {
font-size: 0.875rem;
color: var(–gray-500);
}

.input-actions {
display: flex;
gap: 1rem;
}

/* Examples Section */
.nlp-examples-section {
margin: 2rem 0;
}

.examples-header {
text-align: center;
margin-bottom: 2rem;
}

.examples-header h3 {
font-size: 1.5rem;
font-weight: 600;
color: var(–gray-900);
margin-bottom: 0.5rem;
}

.examples-header p {
color: var(–gray-600);
}

.examples-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
gap: 1.5rem;
}

.example-card {
background: white;
border: 2px solid var(–gray-200);
border-radius: var(–radius-lg);
padding: 1.5rem;
cursor: pointer;
transition: all var(–transition-normal);
position: relative;
overflow: hidden;
}

.example-card:hover {
border-color: var(–primary-300);
transform: translateY(-2px);
box-shadow: var(–shadow-lg);
}

.example-card.selected {
border-color: var(–primary-500);
background: var(–primary-50);
}

.example-card.selected::before {
content: ‘✓’;
position: absolute;
top: 1rem;
right: 1rem;
width: 1.5rem;
height: 1.5rem;
background: var(–primary-500);
color: white;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
font-size: 0.875rem;
font-weight: 600;
}

.example-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 1rem;
}

.example-icon {
font-size: 2rem;
}

.example-category {
background: var(–gray-100);
color: var(–gray-700);
padding: 0.25rem 0.75rem;
border-radius: 1rem;
font-size: 0.75rem;
font-weight: 500;
text-transform: uppercase;
letter-spacing: 0.025em;
}

.example-content h4 {
font-size: 1.125rem;
font-weight: 600;
color: var(–gray-900);
margin-bottom: 0.75rem;
}

.example-content p {
color: var(–gray-600);
line-height: 1.5;
margin-bottom: 1rem;
font-size: 0.875rem;
}

.example-blocks {
display: flex;
flex-wrap: wrap;
gap: 0.5rem;
}

.block-tag {
background: var(–primary-100);
color: var(–primary-700);
padding: 0.25rem 0.5rem;
border-radius: var(–radius-sm);
font-size: 0.75rem;
font-weight: 500;
}

/* Results Section */
.nlp-results-section {
background: white;
border-radius: var(–radius-xl);
padding: 2rem;
box-shadow: var(–shadow-lg);
border: 1px solid var(–gray-200);
animation: slideUp 0.5s ease-out;
}

.results-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 2rem;
padding-bottom: 1rem;
border-bottom: 1px solid var(–gray-200);
}

.results-header h3 {
font-size: 1.5rem;
font-weight: 600;
color: var(–gray-900);
}

.confidence-score {
display: flex;
align-items: center;
gap: 0.75rem;
}

.confidence-label {
font-size: 0.875rem;
font-weight: 500;
color: var(–gray-600);
}

.confidence-bar {
width: 100px;
height: 8px;
background: var(–gray-200);
border-radius: 4px;
overflow: hidden;
}

.confidence-fill {
height: 100%;
transition: width var(–transition-normal);
border-radius: 4px;
}

.confidence-value {
font-size: 0.875rem;
font-weight: 600;
color: var(–gray-900);
min-width: 50px;
}

.results-content {
display: grid;
gap: 2rem;
}

/* Workflow Overview */
.workflow-overview {
display: grid;
grid-template-columns: 1fr 1fr;
gap: 1.5rem;
}

.overview-card {
background: var(–gray-50);
border: 1px solid var(–gray-200);
border-radius: var(–radius-lg);
padding: 1.5rem;
}

.overview-card h4 {
font-size: 1rem;
font-weight: 600;
color: var(–gray-900);
margin-bottom: 0.75rem;
display: flex;
align-items: center;
gap: 0.5rem;
}

.overview-card p {
color: var(–gray-700);
line-height: 1.5;
margin: 0;
}

/* Generated Blocks */
.generated-blocks h4 {
font-size: 1.125rem;
font-weight: 600;
color: var(–gray-900);
margin-bottom: 1rem;
display: flex;
align-items: center;
gap: 0.5rem;
}

.blocks-flow {
display: flex;
align-items: center;
gap: 1rem;
flex-wrap: wrap;
padding: 1.5rem;
background: var(–gray-50);
border-radius: var(–radius-lg);
border: 1px solid var(–gray-200);
}

.block-item {
background: white;
border: 2px solid var(–primary-200);
border-radius: var(–radius-lg);
padding: 1rem;
text-align: center;
min-width: 120px;
transition: all var(–transition-fast);
}

.block-item:hover {
transform: translateY(-2px);
box-shadow: var(–shadow-md);
border-color: var(–primary-400);
}

.block-icon {
font-size: 2rem;
margin-bottom: 0.5rem;
display: block;
}

.block-name {
font-size: 0.875rem;
font-weight: 600;
color: var(–gray-900);
margin-bottom: 0.25rem;
}

.block-type {
font-size: 0.75rem;
color: var(–gray-600);
text-transform: capitalize;
margin-bottom: 0.25rem;
}

.block-confidence {
font-size: 0.75rem;
color: var(–primary-600);
font-weight: 500;
}

.block-connector {
font-size: 1.5rem;
color: var(–primary-500);
font-weight: 600;
flex-shrink: 0;
}

/* Configuration Preview */
.config-preview h4 {
font-size: 1.125rem;
font-weight: 600;
color: var(–gray-900);
margin-bottom: 1rem;
display: flex;
align-items: center;
gap: 0.5rem;
}

.config-cards {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
gap: 1rem;
}

.config-card {
background: var(–gray-50);
border: 1px solid var(–gray-200);
border-radius: var(–radius-md);
padding: 1rem;
text-align: center;
}

.config-label {
font-size: 0.75rem;
color: var(–gray-600);
margin-bottom: 0.5rem;
text-transform: uppercase;
letter-spacing: 0.025em;
}

.config-value {
font-size: 1rem;
font-weight: 600;
color: var(–gray-900);
}

/* Results Actions */
.results-actions {
display: flex;
justify-content: center;
gap: 1rem;
padding-top: 2rem;
border-top: 1px solid var(–gray-200);
}

/* Tips Section */
.nlp-tips-section {
background: white;
border-radius: var(–radius-xl);
padding: 2rem;
box-shadow: var(–shadow-sm);
border: 1px solid var(–gray-200);
}

.tips-header {
text-align: center;
margin-bottom: 2rem;
}

.tips-header h3 {
font-size: 1.5rem;
font-weight: 600;
color: var(–gray-900);
}

.tips-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
gap: 1.5rem;
}

.tip-card {
background: var(–gray-50);
border: 1px solid var(–gray-200);
border-radius: var(–radius-lg);
padding: 1.5rem;
text-align: center;
transition: all var(–transition-fast);
}

.tip-card:hover {
transform: translateY(-2px);
box-shadow: var(–shadow-md);
border-color: var(–primary-300);
}

.tip-icon {
font-size: 2.5rem;
margin-bottom: 1rem;
display: block;
}

.tip-card h4 {
font-size: 1rem;
font-weight: 600;
color: var(–gray-900);
margin-bottom: 0.75rem;
}

.tip-card p {
color: var(–gray-600);
font-size: 0.875rem;
line-height: 1.5;
margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
.nlp-builder {
padding: 1rem 0;
}

.nlp-container {
padding: 0 1rem;
}

.nlp-header {
padding: 2rem 1.5rem;
}

.nlp-header h1 {
font-size: 2rem;
}

.nlp-header p {
font-size: 1rem;
}

.nlp-features {
flex-direction: column;
align-items: center;
gap: 1rem;
}

.examples-grid,
.tips-grid {
grid-template-columns: 1fr;
}

.workflow-overview {
grid-template-columns: 1fr;
}

.config-cards {
grid-template-columns: 1fr;
}

.blocks-flow {
flex-direction: column;
align-items: center;
}

.block-connector {
transform: rotate(90deg);
}

.results-actions {
flex-direction: column;
align-items: center;
}

.input-footer {
flex-direction: column;
gap: 1rem;
align-items: flex-start;
}

.input-actions {
width: 100%;
justify-content: flex-end;
}
}

@media (max-width: 480px) {
.nlp-header {
padding: 1.5rem 1rem;
}

.nlp-input-section,
.nlp-results-section,
.nlp-tips-section {
padding: 1.5rem;
}

.example-card,
.tip-card {
padding: 1rem;
}

.blocks-flow {
padding: 1rem;
}

.block-item {
min-width: 100px;
padding: 0.75rem;
}

.confidence-score {
flex-direction: column;
gap: 0.5rem;
align-items: flex-start;
}

.confidence-bar {
width: 150px;
}
}

/* Animation for results appearance */
@keyframes slideUp {
from {
opacity: 0;
transform: translateY(30px);
}
to {
opacity: 1;
transform: translateY(0);
}
}

/* Loading states */
.nlp-input-section.processing {
position: relative;
overflow: hidden;
}

.nlp-input-section.processing::before {
content: ‘’;
position: absolute;
top: 0;
left: -100%;
width: 100%;
height: 100%;
background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
animation: shimmer 2s infinite;
}

@keyframes shimmer {
0% {
left: -100%;
}
100% {
left: 100%;
}
}

/* High confidence highlighting */
.confidence-fill[style*=“100%”],
.confidence-fill[style*=“9”] {
background: linear-gradient(90deg, var(–success-500), var(–success-400));
}

.confidence-fill[style*=“8”],
.confidence-fill[style*=“7”] {
background: linear-gradient(90deg, var(–primary-500), var(–primary-400));
}

.confidence-fill[style*=“6”],
.confidence-fill[style*=“5”] {
background: linear-gradient(90deg, var(–warning-500), var(–warning-400));
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
.example-card,
.tip-card,
.block-item {
transition: none;
}

.nlp-results-section {
animation: none;
}
}