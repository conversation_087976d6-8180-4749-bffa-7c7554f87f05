# backend/middleware.py

from fastapi import Request, HTTPException, status
from fastapi.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
import time
import asyncio
from collections import defaultdict, deque
from typing import Dict, Deque
import logging
import hashlib

logger = logging.getLogger(**name**)

class RateLimitMiddleware(BaseHTTPMiddleware):
def **init**(self, app, calls: int = 100, period: int = 60):
super().**init**(app)
self.calls = calls
self.period = period
self.clients: Dict[str, Deque[float]] = defaultdict(deque)

```
def get_client_ip(self, request: Request) -> str:
    """Get client IP address"""
    # Check for forwarded IP first (behind proxy)
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip
        
    return request.client.host if request.client else "unknown"
    
async def dispatch(self, request: Request, call_next):
    # Skip rate limiting for health checks and static files
    if request.url.path in ["/health", "/docs", "/openapi.json"]:
        return await call_next(request)
        
    client_ip = self.get_client_ip(request)
    now = time.time()
    
    # Clean old entries
    client_calls = self.clients[client_ip]
    while client_calls and client_calls[0] <= now - self.period:
        client_calls.popleft()
        
    # Check rate limit
    if len(client_calls) >= self.calls:
        logger.warning(f"Rate limit exceeded for IP: {client_ip}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail={
                "error": "Rate limit exceeded",
                "retry_after": int(self.period - (now - client_calls[0]))
            }
        )
        
    # Add current request
    client_calls.append(now)
    
    # Process request
    response = await call_next(request)
    
    # Add rate limit headers
    response.headers["X-RateLimit-Limit"] = str(self.calls)
    response.headers["X-RateLimit-Remaining"] = str(self.calls - len(client_calls))
    response.headers["X-RateLimit-Reset"] = str(int(now + self.period))
    
    return response
```

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
async def dispatch(self, request: Request, call_next):
response = await call_next(request)

```
    # Add security headers
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
    response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"
    
    # Only add HSTS in production
    if request.url.scheme == "https":
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
    return response
```

class LoggingMiddleware(BaseHTTPMiddleware):
async def dispatch(self, request: Request, call_next):
start_time = time.time()
client_ip = self.get_client_ip(request)

```
    # Log request
    logger.info(f"Request: {request.method} {request.url.path} from {client_ip}")
    
    try:
        response = await call_next(request)
        process_time = time.time() - start_time
        
        # Log response
        logger.info(
            f"Response: {response.status_code} for {request.method} {request.url.path} "
            f"({process_time:.3f}s)"
        )
        
        response.headers["X-Process-Time"] = str(process_time)
        return response
        
    except Exception as e:
        process_time = time.time() - start_time
        logger.error(
            f"Error: {str(e)} for {request.method} {request.url.path} "
            f"({process_time:.3f}s)"
        )
        raise
        
def get_client_ip(self, request: Request) -> str:
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()
    return request.client.host if request.client else "unknown"
```

-----

# Add to backend/main.py

from middleware import RateLimitMiddleware, SecurityHeadersMiddleware, LoggingMiddleware

# Add middleware (order matters!)

app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(LoggingMiddleware)
app.add_middleware(RateLimitMiddleware, calls=100, period=60)  # 100 requests per minute