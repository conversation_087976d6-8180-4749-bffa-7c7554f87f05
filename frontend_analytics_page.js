// frontend/src/pages/Analytics.js
import React, { useState, useEffect } from ‘react’;
import { useWorkflow } from ‘../contexts/WorkflowContext’;
import LoadingSpinner from ‘../components/LoadingSpinner’;
import ‘./Analytics.css’;

const Analytics = () => {
const { workflows, fetchWorkflows } = useWorkflow();
const [timeRange, setTimeRange] = useState(‘7d’);
const [loading, setLoading] = useState(true);
const [analyticsData, setAnalyticsData] = useState({
overview: {
totalExecutions: 0,
successfulExecutions: 0,
failedExecutions: 0,
avgExecutionTime: 0,
totalTokensUsed: 0,
totalCost: 0
},
performance: {
executionTrend: [],
successRate: [],
responseTime: [],
tokenUsage: []
},
topWorkflows: [],
costBreakdown: [],
recentActivity: []
});

useEffect(() => {
fetchWorkflows();
generateMockAnalytics();
}, [fetchWorkflows, timeRange]);

const generateMockAnalytics = () => {
setLoading(true);

```
// Simulate API call
setTimeout(() => {
  const mockData = {
    overview: {
      totalExecutions: 1247,
      successfulExecutions: 1201,
      failedExecutions: 46,
      avgExecutionTime: 2.3,
      totalTokensUsed: 89456,
      totalCost: 35.78
    },
    performance: {
      executionTrend: generateTimeSeriesData('executions'),
      successRate: generateTimeSeriesData('success'),
      responseTime: generateTimeSeriesData('time'),
      tokenUsage: generateTimeSeriesData('tokens')
    },
    topWorkflows: [
      { name: 'Email Auto-Responder', executions: 456, successRate: 98.2, avgTime: 1.8 },
      { name: 'Social Media Manager', executions: 324, successRate: 95.7, avgTime: 3.2 },
      { name: 'Daily Report Generator', executions: 289, successRate: 99.1, avgTime: 4.1 },
      { name: 'Lead Qualifier', executions: 178, successRate: 92.4, avgTime: 2.7 }
    ],
    costBreakdown: [
      { category: 'AI Text Generation', cost: 18.45, percentage: 51.6 },
      { category: 'Email Processing', cost: 8.92, percentage: 24.9 },
      { category: 'Web Scraping', cost: 5.23, percentage: 14.6 },
      { category: 'Social Media API', cost: 3.18, percentage: 8.9 }
    ],
    recentActivity: [
      { time: '2 minutes ago', action: 'Workflow executed', workflow: 'Email Auto-Responder', status: 'success' },
      { time: '15 minutes ago', action: 'Workflow failed', workflow: 'Lead Qualifier', status: 'error' },
      { time: '1 hour ago', action: 'Workflow executed', workflow: 'Social Media Manager', status: 'success' },
      { time: '2 hours ago', action: 'Workflow executed', workflow: 'Daily Report Generator', status: 'success' }
    ]
  };
  
  setAnalyticsData(mockData);
  setLoading(false);
}, 1000);
```

};

const generateTimeSeriesData = (type) => {
const days = timeRange === ‘24h’ ? 24 : timeRange === ‘7d’ ? 7 : timeRange === ‘30d’ ? 30 : 90;
const data = [];

```
for (let i = days - 1; i >= 0; i--) {
  const date = new Date();
  date.setDate(date.getDate() - i);
  
  let value;
  switch (type) {
    case 'executions':
      value = Math.floor(Math.random() * 50) + 10;
      break;
    case 'success':
      value = Math.floor(Math.random() * 10) + 90;
      break;
    case 'time':
      value = Math.random() * 2 + 1;
      break;
    case 'tokens':
      value = Math.floor(Math.random() * 2000) + 500;
      break;
    default:
      value = Math.random() * 100;
  }
  
  data.push({
    date: date.toISOString().split('T')[0],
    value: value
  });
}

return data;
```

};

const timeRangeOptions = [
{ value: ‘24h’, label: ‘Last 24 Hours’ },
{ value: ‘7d’, label: ‘Last 7 Days’ },
{ value: ‘30d’, label: ‘Last 30 Days’ },
{ value: ‘90d’, label: ‘Last 90 Days’ }
];

const formatCurrency = (amount) => {
return new Intl.NumberFormat(‘en-US’, {
style: ‘currency’,
currency: ‘USD’
}).format(amount);
};

const formatNumber = (num) => {
return new Intl.NumberFormat(‘en-US’).format(num);
};

if (loading) {
return <LoadingSpinner message="Loading analytics..." />;
}

return (
<div className="analytics-page">
<div className="analytics-container">
{/* Header */}
<div className="analytics-header">
<div className="header-content">
<h1>📈 Analytics & Insights</h1>
<p>Monitor your automation performance and AI usage</p>
</div>

```
      <div className="header-controls">
        <select
          value={timeRange}
          onChange={(e) => setTimeRange(e.target.value)}
          className="time-range-select"
        >
          {timeRangeOptions.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        
        <button className="btn-secondary">
          <span className="btn-icon">📊</span>
          Export Report
        </button>
      </div>
    </div>

    {/* Overview Cards */}
    <div className="overview-grid">
      <div className="overview-card">
        <div className="card-header">
          <h3>Total Executions</h3>
          <span className="card-icon">🔄</span>
        </div>
        <div className="card-value">{formatNumber(analyticsData.overview.totalExecutions)}</div>
        <div className="card-trend positive">
          <span>↗</span> +12% from last period
        </div>
      </div>
      
      <div className="overview-card">
        <div className="card-header">
          <h3>Success Rate</h3>
          <span className="card-icon">✅</span>
        </div>
        <div className="card-value">
          {((analyticsData.overview.successfulExecutions / analyticsData.overview.totalExecutions) * 100).toFixed(1)}%
        </div>
        <div className="card-trend positive">
          <span>↗</span> +2.3% from last period
        </div>
      </div>
      
      <div className="overview-card">
        <div className="card-header">
          <h3>Avg Response Time</h3>
          <span className="card-icon">⚡</span>
        </div>
        <div className="card-value">{analyticsData.overview.avgExecutionTime}s</div>
        <div className="card-trend positive">
          <span>↘</span> 15% faster
        </div>
      </div>
      
      <div className="overview-card">
        <div className="card-header">
          <h3>Total Cost</h3>
          <span className="card-icon">💰</span>
        </div>
        <div className="card-value">{formatCurrency(analyticsData.overview.totalCost)}</div>
        <div className="card-trend negative">
          <span>↗</span> +8.2% from last period
        </div>
      </div>
    </div>

    {/* Performance Charts */}
    <div className="charts-grid">
      <div className="chart-card">
        <div className="chart-header">
          <h3>Execution Trend</h3>
          <span className="chart-info">Daily workflow executions</span>
        </div>
        <div className="chart-container">
          <div className="mini-chart">
            {analyticsData.performance.executionTrend.map((point, index) => (
              <div
                key={index}
                className="chart-bar"
                style={{
                  height: `${(point.value / 60) * 100}%`,
                  backgroundColor: 'var(--primary-500)'
                }}
                title={`${point.date}: ${point.value} executions`}
              ></div>
            ))}
          </div>
        </div>
      </div>
      
      <div className="chart-card">
        <div className="chart-header">
          <h3>Success Rate</h3>
          <span className="chart-info">Execution success percentage</span>
        </div>
        <div className="chart-container">
          <div className="mini-chart">
            {analyticsData.performance.successRate.map((point, index) => (
              <div
                key={index}
                className="chart-bar"
                style={{
                  height: `${point.value}%`,
                  backgroundColor: 'var(--success-500)'
                }}
                title={`${point.date}: ${point.value}% success rate`}
              ></div>
            ))}
          </div>
        </div>
      </div>
      
      <div className="chart-card">
        <div className="chart-header">
          <h3>Response Time</h3>
          <span className="chart-info">Average execution time (seconds)</span>
        </div>
        <div className="chart-container">
          <div className="mini-chart">
            {analyticsData.performance.responseTime.map((point, index) => (
              <div
                key={index}
                className="chart-bar"
                style={{
                  height: `${(point.value / 5) * 100}%`,
                  backgroundColor: 'var(--warning-500)'
                }}
                title={`${point.date}: ${point.value.toFixed(2)}s avg time`}
              ></div>
            ))}
          </div>
        </div>
      </div>
      
      <div className="chart-card">
        <div className="chart-header">
          <h3>Token Usage</h3>
          <span className="chart-info">AI tokens consumed daily</span>
        </div>
        <div className="chart-container">
          <div className="mini-chart">
            {analyticsData.performance.tokenUsage.map((point, index) => (
              <div
                key={index}
                className="chart-bar"
                style={{
                  height: `${(point.value / 2500) * 100}%`,
                  backgroundColor: '#8b5cf6'
                }}
                title={`${point.date}: ${formatNumber(point.value)} tokens`}
              ></div>
            ))}
          </div>
        </div>
      </div>
    </div>

    {/* Detailed Analytics */}
    <div className="details-grid">
      {/* Top Workflows */}
      <div className="details-card">
        <div className="card-header">
          <h3>🏆 Top Performing Workflows</h3>
        </div>
        <div className="workflows-list">
          {analyticsData.topWorkflows.map((workflow, index) => (
            <div key={index} className="workflow-item">
              <div className="workflow-rank">#{index + 1}</div>
              <div className="workflow-info">
                <div className="workflow-name">{workflow.name}</div>
                <div className="workflow-stats">
                  {formatNumber(workflow.executions)} executions • {workflow.successRate}% success • {workflow.avgTime}s avg
                </div>
              </div>
              <div className="workflow-score">
                <div className="score-bar">
                  <div 
                    className="score-fill"
                    style={{ width: `${workflow.successRate}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Cost Breakdown */}
      <div className="details-card">
        <div className="card-header">
          <h3>💰 Cost Breakdown</h3>
        </div>
        <div className="cost-list">
          {analyticsData.costBreakdown.map((item, index) => (
            <div key={index} className="cost-item">
              <div className="cost-info">
                <div className="cost-category">{item.category}</div>
                <div className="cost-amount">{formatCurrency(item.cost)}</div>
              </div>
              <div className="cost-bar">
                <div 
                  className="cost-fill"
                  style={{ 
                    width: `${item.percentage}%`,
                    backgroundColor: `hsl(${index * 60}, 70%, 50%)`
                  }}
                ></div>
              </div>
              <div className="cost-percentage">{item.percentage}%</div>
            </div>
          ))}
        </div>
        
        <div className="cost-summary">
          <div className="summary-item">
            <span>Total this period:</span>
            <strong>{formatCurrency(analyticsData.overview.totalCost)}</strong>
          </div>
          <div className="summary-item">
            <span>Tokens used:</span>
            <strong>{formatNumber(analyticsData.overview.totalTokensUsed)}</strong>
          </div>
          <div className="summary-item">
            <span>Avg cost per token:</span>
            <strong>${(analyticsData.overview.totalCost / analyticsData.overview.totalTokensUsed).toFixed(6)}</strong>
          </div>
        </div>
      </div>
    </div>

    {/* Recent Activity */}
    <div className="activity-section">
      <div className="section-header">
        <h3>🕐 Recent Activity</h3>
        <button className="btn-outline">View All</button>
      </div>
      
      <div className="activity-list">
        {analyticsData.recentActivity.map((activity, index) => (
          <div key={index} className="activity-item">
            <div className={`activity-status ${activity.status}`}>
              {activity.status === 'success' ? '✅' : '❌'}
            </div>
            <div className="activity-content">
              <div className="activity-action">{activity.action}</div>
              <div className="activity-details">
                <span className="workflow-name">{activity.workflow}</span>
                <span className="activity-time">{activity.time}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>

    {/* Performance Insights */}
    <div className="insights-section">
      <div className="section-header">
        <h3>💡 Performance Insights</h3>
      </div>
      
      <div className="insights-grid">
        <div className="insight-card">
          <div className="insight-icon">🚀</div>
          <div className="insight-content">
            <h4>Optimization Opportunity</h4>
            <p>Your "Email Auto-Responder" workflow is performing 23% faster than average. Consider applying similar optimizations to other workflows.</p>
          </div>
        </div>
        
        <div className="insight-card">
          <div className="insight-icon">⚠️</div>
          <div className="insight-content">
            <h4>High Failure Rate</h4>
            <p>The "Lead Qualifier" workflow has a 7.6% failure rate. Review error logs and consider adding retry logic.</p>
          </div>
        </div>
        
        <div className="insight-card">
          <div className="insight-icon">💰</div>
          <div className="insight-content">
            <h4>Cost Optimization</h4>
            <p>Your AI token usage is 15% below similar users. You could increase automation frequency while staying cost-efficient.</p>
          </div>
        </div>
      </div>
    </div>

    {/* Performance Recommendations */}
    <div className="recommendations-section">
      <div className="section-header">
        <h3>🎯 Recommendations</h3>
      </div>
      
      <div className="recommendations-list">
        <div className="recommendation-item">
          <div className="recommendation-priority high">High</div>
          <div className="recommendation-content">
            <h4>Enable Auto-Retry for Critical Workflows</h4>
            <p>Add automatic retry logic to workflows with failure rates above 5% to improve reliability.</p>
          </div>
          <button className="btn-primary small">Apply</button>
        </div>
        
        <div className="recommendation-item">
          <div className="recommendation-priority medium">Medium</div>
          <div className="recommendation-content">
            <h4>Optimize AI Model Selection</h4>
            <p>Switch to faster models for simple text generation tasks to reduce response time by up to 40%.</p>
          </div>
          <button className="btn-secondary small">Review</button>
        </div>
        
        <div className="recommendation-item">
          <div className="recommendation-priority low">Low</div>
          <div className="recommendation-content">
            <h4>Schedule Maintenance Windows</h4>
            <p>Consider scheduling non-critical workflows during off-peak hours to reduce costs.</p>
          </div>
          <button className="btn-outline small">Learn More</button>
        </div>
      </div>
    </div>
  </div>
</div>
```

);
};

export default Analytics;