"""
Tests for Database Models
"""

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from backend_models import Base, User, Workflow, Block, Execution, ApiKey, WorkflowTemplate
from datetime import datetime

# Test database setup
TEST_DATABASE_URL = "sqlite:///:memory:"

@pytest.fixture
def db_session():
    """Create a test database session"""
    engine = create_engine(TEST_DATABASE_URL, connect_args={"check_same_thread": False})
    Base.metadata.create_all(bind=engine)
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = SessionLocal()
    
    yield session
    
    session.close()

class TestUserModel:
    """Test User model"""
    
    def test_create_user(self, db_session):
        """Test creating a user"""
        user = User(
            email="<EMAIL>",
            name="Test User",
            hashed_password="hashed_password_123"
        )
        
        db_session.add(user)
        db_session.commit()
        
        assert user.id is not None
        assert user.email == "<EMAIL>"
        assert user.name == "Test User"
        assert user.is_active is True
        assert user.is_premium is False
        assert user.created_at is not None
        
    def test_user_email_unique(self, db_session):
        """Test that user emails must be unique"""
        user1 = User(
            email="<EMAIL>",
            name="Test User 1",
            hashed_password="hash1"
        )
        user2 = User(
            email="<EMAIL>",
            name="Test User 2", 
            hashed_password="hash2"
        )
        
        db_session.add(user1)
        db_session.commit()
        
        db_session.add(user2)
        
        with pytest.raises(Exception):  # Should raise integrity error
            db_session.commit()

class TestWorkflowModel:
    """Test Workflow model"""
    
    def test_create_workflow(self, db_session):
        """Test creating a workflow"""
        # First create a user
        user = User(
            email="<EMAIL>",
            name="Test User",
            hashed_password="hashed_password"
        )
        db_session.add(user)
        db_session.commit()
        
        # Create workflow
        workflow = Workflow(
            name="Test Workflow",
            description="A test workflow",
            config={"blocks": [], "connections": []},
            user_id=user.id
        )
        
        db_session.add(workflow)
        db_session.commit()
        
        assert workflow.id is not None
        assert workflow.name == "Test Workflow"
        assert workflow.description == "A test workflow"
        assert workflow.is_active is True
        assert workflow.is_public is False
        assert workflow.user_id == user.id
        assert workflow.created_at is not None
        
    def test_workflow_user_relationship(self, db_session):
        """Test workflow-user relationship"""
        user = User(
            email="<EMAIL>",
            name="Test User",
            hashed_password="hashed_password"
        )
        db_session.add(user)
        db_session.commit()
        
        workflow = Workflow(
            name="Test Workflow",
            description="A test workflow",
            user_id=user.id
        )
        db_session.add(workflow)
        db_session.commit()
        
        # Test relationship
        assert workflow.user == user
        assert workflow in user.workflows

class TestBlockModel:
    """Test Block model"""
    
    def test_create_block(self, db_session):
        """Test creating a block"""
        # Create user and workflow first
        user = User(
            email="<EMAIL>",
            name="Test User",
            hashed_password="hashed_password"
        )
        db_session.add(user)
        db_session.commit()
        
        workflow = Workflow(
            name="Test Workflow",
            user_id=user.id
        )
        db_session.add(workflow)
        db_session.commit()
        
        # Create block
        block = Block(
            workflow_id=workflow.id,
            block_type="ai_text_generator",
            name="AI Generator",
            config={"model": "llama-4-scout-17b-16e-instruct"},
            position={"x": 100, "y": 200}
        )
        
        db_session.add(block)
        db_session.commit()
        
        assert block.id is not None
        assert block.workflow_id == workflow.id
        assert block.block_type == "ai_text_generator"
        assert block.name == "AI Generator"
        assert block.config["model"] == "llama-4-scout-17b-16e-instruct"
        assert block.position["x"] == 100

class TestExecutionModel:
    """Test Execution model"""
    
    def test_create_execution(self, db_session):
        """Test creating an execution"""
        # Create user and workflow
        user = User(
            email="<EMAIL>",
            name="Test User",
            hashed_password="hashed_password"
        )
        db_session.add(user)
        db_session.commit()
        
        workflow = Workflow(
            name="Test Workflow",
            user_id=user.id
        )
        db_session.add(workflow)
        db_session.commit()
        
        # Create execution
        execution = Execution(
            workflow_id=workflow.id,
            user_id=user.id,
            status="running",
            trigger="manual"
        )
        
        db_session.add(execution)
        db_session.commit()
        
        assert execution.id is not None
        assert execution.workflow_id == workflow.id
        assert execution.user_id == user.id
        assert execution.status == "running"
        assert execution.trigger == "manual"
        assert execution.started_at is not None

class TestApiKeyModel:
    """Test ApiKey model"""
    
    def test_create_api_key(self, db_session):
        """Test creating an API key"""
        user = User(
            email="<EMAIL>",
            name="Test User",
            hashed_password="hashed_password"
        )
        db_session.add(user)
        db_session.commit()
        
        api_key = ApiKey(
            user_id=user.id,
            name="Cerebras API Key",
            provider="cerebras",
            api_key="encrypted_key_here"
        )
        
        db_session.add(api_key)
        db_session.commit()
        
        assert api_key.id is not None
        assert api_key.user_id == user.id
        assert api_key.name == "Cerebras API Key"
        assert api_key.provider == "cerebras"
        assert api_key.is_active is True
        assert api_key.usage_count == 0

if __name__ == "__main__":
    pytest.main([__file__])
