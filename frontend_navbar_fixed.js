import React from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from './frontend_auth_context_fixed';

const Navbar = () => {
  const { user, isAuthenticated, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const isActive = (path) => location.pathname === path;

  return (
    <nav className="navbar">
      <div className="navbar-container">
        {/* Logo */}
        <Link to="/" className="navbar-brand">
          <span className="brand-icon">🤖</span>
          AutoGPT
        </Link>

        {/* Navigation Links */}
        <div className="navbar-menu">
          {isAuthenticated ? (
            <>
              <Link 
                to="/dashboard" 
                className={`navbar-link ${isActive('/dashboard') ? 'active' : ''}`}
              >
                Dashboard
              </Link>
              <Link 
                to="/workflows" 
                className={`navbar-link ${isActive('/workflows') ? 'active' : ''}`}
              >
                Workflows
              </Link>
              <Link 
                to="/nlp-builder" 
                className={`navbar-link ${isActive('/nlp-builder') ? 'active' : ''}`}
              >
                NLP Builder
              </Link>
              <Link 
                to="/analytics" 
                className={`navbar-link ${isActive('/analytics') ? 'active' : ''}`}
              >
                Analytics
              </Link>
              
              {/* User Menu */}
              <div className="navbar-user">
                <span className="user-name">
                  {user?.name || 'User'}
                </span>
                <button onClick={handleLogout} className="logout-btn">
                  Logout
                </button>
              </div>
            </>
          ) : (
            <>
              <Link to="/login" className="navbar-link">
                Login
              </Link>
              <Link to="/register" className="navbar-link register-btn">
                Register
              </Link>
            </>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
