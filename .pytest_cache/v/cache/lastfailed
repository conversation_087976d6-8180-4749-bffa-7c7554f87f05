{"test_backend_api.py::TestAuthEndpoints::test_register_user": true, "test_backend_api.py::TestAuthEndpoints::test_register_duplicate_email": true, "test_backend_api.py::TestAuthEndpoints::test_login_success": true, "test_backend_api.py::TestAuthEndpoints::test_login_invalid_credentials": true, "test_backend_api.py::TestAuthEndpoints::test_login_nonexistent_user": true, "test_backend_api.py::TestProtectedEndpoints::test_get_current_user": true, "test_backend_api.py::TestProtectedEndpoints::test_get_workflows_empty": true, "test_backend_api.py::TestProtectedEndpoints::test_create_workflow": true, "test_backend_api.py::TestProtectedEndpoints::test_unauthorized_access": true, "test_backend_api.py::TestNLPEndpoints::test_process_nlp_unauthorized": true, "test_backend_api.py::TestNLPEndpoints::test_process_nlp_authorized": true, "test_backend_api.py::TestAIEndpoints::test_get_available_models": true}