["test_backend_api.py::TestAIEndpoints::test_get_available_models", "test_backend_api.py::TestAuthEndpoints::test_login_invalid_credentials", "test_backend_api.py::TestAuthEndpoints::test_login_nonexistent_user", "test_backend_api.py::TestAuthEndpoints::test_login_success", "test_backend_api.py::TestAuthEndpoints::test_register_duplicate_email", "test_backend_api.py::TestAuthEndpoints::test_register_user", "test_backend_api.py::TestHealthEndpoint::test_health_check", "test_backend_api.py::TestNLPEndpoints::test_process_nlp_authorized", "test_backend_api.py::TestNLPEndpoints::test_process_nlp_unauthorized", "test_backend_api.py::TestProtectedEndpoints::test_create_workflow", "test_backend_api.py::TestProtectedEndpoints::test_get_current_user", "test_backend_api.py::TestProtectedEndpoints::test_get_workflows_empty", "test_backend_api.py::TestProtectedEndpoints::test_unauthorized_access", "test_backend_auth.py::TestJWTTokens::test_create_access_token", "test_backend_auth.py::TestJWTTokens::test_create_token_with_custom_expiry", "test_backend_auth.py::TestJWTTokens::test_verify_invalid_token", "test_backend_auth.py::TestJWTTokens::test_verify_valid_token", "test_backend_auth.py::TestPasswordHashing::test_password_hashing", "test_backend_auth.py::TestPasswordHashing::test_password_verification_failure", "test_backend_auth.py::TestPasswordHashing::test_password_verification_success", "test_backend_auth.py::TestTokenSecurity::test_token_contains_required_claims", "test_backend_auth.py::TestTokenSecurity::test_tokens_are_unique", "test_backend_models.py::TestApiKeyModel::test_create_api_key", "test_backend_models.py::TestBlockModel::test_create_block", "test_backend_models.py::TestExecutionModel::test_create_execution", "test_backend_models.py::TestUserModel::test_create_user", "test_backend_models.py::TestUserModel::test_user_email_unique", "test_backend_models.py::TestWorkflowModel::test_create_workflow", "test_backend_models.py::TestWorkflowModel::test_workflow_user_relationship"]