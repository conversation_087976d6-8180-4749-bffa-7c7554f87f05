"""
Database Configuration and Connection
File: backend/database.py
"""

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os

# Database URL from environment variable

DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./autogpt.db")

# Handle SQLite URL format for compatibility

if DATABASE_URL.startswith("sqlite"):
    engine = create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False}  # SQLite specific
    )
else:
    # PostgreSQL or other databases
    engine = create_engine(DATABASE_URL)

# Session factory

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models

Base = declarative_base()

def get_db():
    """Dependency to get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_db():
    """Initialize database tables"""
    from models import Base
    Base.metadata.create_all(bind=engine)

def create_test_data():
    """Create test data for development"""
    from models import User, Workflow, Block, WorkflowTemplate
    from auth import get_password_hash
    from sqlalchemy.orm import Session

    db = SessionLocal()

    try:
        # Check if test user already exists
        test_user = db.query(User).filter(User.email == "<EMAIL>").first()
        
        if not test_user:
            # Create test user
            test_user = User(
                email="<EMAIL>",
                name="Test User",
                hashed_password=get_password_hash("password123"),
                is_active=True
            )
            db.add(test_user)
            db.commit()
            db.refresh(test_user)
            
            # Create sample workflow
            sample_workflow = Workflow(
                name="Email Auto-Responder",
                description="Automatically respond to customer emails using AI",
                config={
                    "settings": {
                        "auto_retry": True,
                        "timeout": 300
                    },
                    "connections": [
                        {
                            "from_block": "block_1",
                            "to_block": "block_2",
                            "from_output": "emails",
                            "to_input": "input"
                        },
                        {
                            "from_block": "block_2",
                            "to_block": "block_3",
                            "from_output": "generated_text",
                            "to_input": "body"
                        }
                    ]
                },
                user_id=test_user.id,
                category="communication"
            )
            db.add(sample_workflow)
            db.commit()
            db.refresh(sample_workflow)
            
            # Create sample blocks
            blocks = [
                Block(
                    workflow_id=sample_workflow.id,
                    name="Gmail Reader",
                    type="email_reader",
                    config={
                        "provider": "gmail",
                        "folder": "inbox",
                        "max_emails": 10,
                        "filters": {
                            "unread": True,
                            "from_contains": "customer"
                        }
                    },
                    position_x=100,
                    position_y=100
                ),
                Block(
                    workflow_id=sample_workflow.id,
                    name="AI Response Generator",
                    type="ai_text_generator",
                    config={
                        "model": "llama-4-scout-17b-16e-instruct",
                        "prompt": "Generate a helpful customer service response to the following email:",
                        "temperature": 0.7,
                        "max_tokens": 500,
                        "tone": "professional and friendly"
                    },
                    position_x=300,
                    position_y=100
                ),
                Block(
                    workflow_id=sample_workflow.id,
                    name="Email Sender",
                    type="email_sender",
                    config={
                        "provider": "gmail",
                        "reply_to_original": True,
                        "add_signature": True
                    },
                    position_x=500,
                    position_y=100
                )
            ]
            
            for block in blocks:
                db.add(block)
            
            db.commit()
            
            print("✅ Test data created successfully!")
            print("🔐 Test login: <EMAIL> / password123")
            
    except Exception as e:
        print(f"❌ Error creating test data: {e}")
        db.rollback()
    finally:
        db.close()

def create_workflow_templates():
    """Create default workflow templates"""
    from models import WorkflowTemplate, User
    from sqlalchemy.orm import Session

    db = SessionLocal()

    try:
        # Check if templates already exist
        existing_templates = db.query(WorkflowTemplate).count()
        if existing_templates > 0:
            return
        
        templates = [
            {
                "name": "Email Auto-Responder",
                "description": "Automatically read emails, generate AI responses, and send them back",
                "category": "communication",
                "is_featured": True,
                "template_data": {
                    "blocks": [
                        {
                            "type": "email_reader",
                            "name": "Email Reader",
                            "config": {
                                "provider": "gmail",
                                "folder": "inbox",
                                "max_emails": 10
                            },
                            "position": {"x": 100, "y": 100}
                        },
                        {
                            "type": "ai_text_generator",
                            "name": "AI Response Generator",
                            "config": {
                                "model": "llama-4-scout-17b-16e-instruct",
                                "prompt": "Generate a professional response to this email",
                                "temperature": 0.7
                            },
                            "position": {"x": 300, "y": 100}
                        },
                        {
                            "type": "email_sender",
                            "name": "Email Sender",
                            "config": {
                                "provider": "gmail",
                                "reply_to_original": True
                            },
                            "position": {"x": 500, "y": 100}
                        }
                    ],
                    "connections": [
                        {"from": 0, "to": 1, "from_output": "emails", "to_input": "context"},
                        {"from": 1, "to": 2, "from_output": "generated_text", "to_input": "body"}
                    ]
                }
            },
            {
                "name": "Social Media Manager",
                "description": "Monitor trends, generate content, and post to social platforms",
                "category": "social_media",
                "is_featured": True,
                "template_data": {
                    "blocks": [
                        {
                            "type": "web_scraper",
                            "name": "Trend Monitor",
                            "config": {
                                "url": "https://trends.google.com",
                                "selectors": [".trending-topics"]
                            },
                            "position": {"x": 100, "y": 100}
                        },
                        {
                            "type": "ai_text_generator",
                            "name": "Content Creator",
                            "config": {
                                "model": "llama-4-scout-17b-16e-instruct",
                                "prompt": "Create engaging social media content about these trending topics",
                                "temperature": 0.8
                            },
                            "position": {"x": 300, "y": 100}
                        },
                        {
                            "type": "social_poster",
                            "name": "Social Poster",
                            "config": {
                                "platforms": ["twitter", "linkedin"],
                                "schedule": "immediate"
                            },
                            "position": {"x": 500, "y": 100}
                        }
                    ],
                    "connections": [
                        {"from": 0, "to": 1, "from_output": "scraped_data", "to_input": "context"},
                        {"from": 1, "to": 2, "from_output": "generated_text", "to_input": "content"}
                    ]
                }
            },
            {
                "name": "Daily Report Generator",
                "description": "Generate and send daily business reports with insights",
                "category": "reporting",
                "is_featured": False,
                "template_data": {
                    "blocks": [
                        {
                            "type": "scheduler",
                            "name": "Daily Trigger",
                            "config": {
                                "frequency": "daily",
                                "time": "09:00"
                            },
                            "position": {"x": 100, "y": 100}
                        },
                        {
                            "type": "file_reader",
                            "name": "Data Reader",
                            "config": {
                                "file_path": "/data/sales_data.csv",
                                "format": "csv"
                            },
                            "position": {"x": 300, "y": 100}
                        },
                        {
                            "type": "ai_text_generator",
                            "name": "Report Generator",
                            "config": {
                                "model": "llama-4-scout-17b-16e-instruct",
                                "prompt": "Analyze this data and create a comprehensive daily business report",
                                "temperature": 0.3
                            },
                            "position": {"x": 500, "y": 100}
                        },
                        {
                            "type": "email_sender",
                            "name": "Report Sender",
                            "config": {
                                "recipient": "<EMAIL>",
                                "subject": "Daily Business Report"
                            },
                            "position": {"x": 700, "y": 100}
                        }
                    ],
                    "connections": [
                        {"from": 0, "to": 1, "from_output": "trigger", "to_input": "trigger"},
                        {"from": 1, "to": 2, "from_output": "file_content", "to_input": "context"},
                        {"from": 2, "to": 3, "from_output": "generated_text", "to_input": "body"}
                    ]
                }
            }
        ]
        
        for template_data in templates:
            template = WorkflowTemplate(**template_data)
            db.add(template)
        
        db.commit()
        print("✅ Workflow templates created successfully!")
        
    except Exception as e:
        print(f"❌ Error creating workflow templates: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("🗄️ Initializing database…")
    init_db()
    print("📊 Creating test data…")
    create_test_data()
    print("📝 Creating workflow templates…")
    create_workflow_templates()
    print("✅ Database setup complete!")
