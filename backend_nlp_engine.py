"""
Natural Language Processing Engine
File: backend/nlp_engine.py
"""

import json
import re
import time
from typing import Dict, List, Any, Tuple
import spacy
from cerebras_provider import CerebrasProvider

class NLPEngine:
    """Processes natural language descriptions and converts them to AutoGPT workflows"""

    def __init__(self, cerebras_provider: CerebrasProvider):
        self.cerebras_provider = cerebras_provider
        self.nlp = self._load_spacy_model()
        self.block_patterns = self._initialize_block_patterns()
        self.workflow_templates = self._initialize_workflow_templates()

    def _load_spacy_model(self):
        """Load spaCy NLP model"""
        try:
            return spacy.load("en_core_web_sm")
        except OSError:
            print("⚠️ spaCy model not found. Installing...")
            import subprocess
            subprocess.run(["python", "-m", "spacy", "download", "en_core_web_sm"])
            return spacy.load("en_core_web_sm")

    def _initialize_block_patterns(self) -> Dict[str, Dict]:
        """Initialize patterns for identifying block types from natural language"""
        return {
            "email_reader": {
                "patterns": [
                    r"\b(read|check|monitor|watch)\b.*\b(email|gmail|inbox|mail)\b",
                    r"\b(email|gmail|mail)\b.*\b(reader|monitor|checker)\b",
                    r"\b(incoming|received|new)\b.*\b(email|message)\b"
                ],
                "keywords": ["email", "gmail", "inbox", "mail", "read", "monitor", "check"],
                "confidence_boost": 0.1
            },
            "email_sender": {
                "patterns": [
                    r"\b(send|reply|respond|email)\b.*\b(email|mail|message)\b",
                    r"\b(auto.?respond|auto.?reply)\b",
                    r"\b(send|dispatch|deliver)\b.*\b(notification|response)\b"
                ],
                "keywords": ["send", "reply", "respond", "email", "auto-respond", "notification"],
                "confidence_boost": 0.1
            },
            "ai_text_generator": {
                "patterns": [
                    r"\b(generate|create|write|compose)\b.*\b(text|content|response|message)\b",
                    r"\b(ai|artificial intelligence|gpt|llm)\b.*\b(generate|create|write)\b",
                    r"\b(smart|intelligent|automated)\b.*\b(response|reply|content)\b"
                ],
                "keywords": ["generate", "ai", "gpt", "text", "content", "create", "write", "smart"],
                "confidence_boost": 0.15
            },
            "web_scraper": {
                "patterns": [
                    r"\b(scrape|extract|crawl|gather)\b.*\b(web|website|page|data)\b",
                    r"\b(monitor|check|watch)\b.*\b(website|page|url)\b",
                    r"\b(collect|fetch|grab)\b.*\b(data|information|content)\b"
                ],
                "keywords": ["scrape", "extract", "web", "website", "data", "crawl", "monitor"],
                "confidence_boost": 0.1
            },
            "social_poster": {
                "patterns": [
                    r"\b(post|share|publish)\b.*\b(social|twitter|facebook|linkedin|instagram)\b",
                    r"\b(social media|social network)\b.*\b(post|share|publish)\b",
                    r"\b(tweet|facebook post|linkedin post)\b"
                ],
                "keywords": ["social", "twitter", "facebook", "linkedin", "post", "share", "publish"],
                "confidence_boost": 0.1
            },
            "scheduler": {
                "patterns": [
                    r"\b(schedule|daily|weekly|monthly|every|regularly)\b",
                    r"\b(trigger|run|execute)\b.*\b(daily|weekly|monthly|hourly)\b",
                    r"\b(automation|automatic)\b.*\b(schedule|time|interval)\b"
                ],
                "keywords": ["schedule", "daily", "weekly", "monthly", "trigger", "automatic", "regularly"],
                "confidence_boost": 0.1
            },
            "conditional": {
                "patterns": [
                    r"\b(if|when|condition|based on|depending on)\b",
                    r"\b(filter|select|choose)\b.*\b(based on|if|when)\b",
                    r"\b(only if|only when|conditional)\b"
                ],
                "keywords": ["if", "when", "condition", "filter", "based on", "conditional"],
                "confidence_boost": 0.1
            },
            "file_reader": {
                "patterns": [
                    r"\b(read|open|load|import)\b.*\b(file|document|csv|excel|pdf)\b",
                    r"\b(file|document|data)\b.*\b(reader|processor|parser)\b"
                ],
                "keywords": ["file", "document", "csv", "excel", "pdf", "read", "import", "data"],
                "confidence_boost": 0.1
            },
            "file_writer": {
                "patterns": [
                    r"\b(save|write|export|create)\b.*\b(file|document|report|csv)\b",
                    r"\b(output|generate|produce)\b.*\b(file|document|report)\b"
                ],
                "keywords": ["save", "write", "export", "file", "document", "report", "output"],
                "confidence_boost": 0.1
            },
            "notification_sender": {
                "patterns": [
                    r"\b(notify|alert|inform|send notification)\b",
                    r"\b(notification|alert|message)\b.*\b(send|deliver|dispatch)\b"
                ],
                "keywords": ["notify", "alert", "notification", "inform", "message"],
                "confidence_boost": 0.1
            },
            "webhook": {
                "patterns": [
                    r"\b(webhook|http|api|trigger|external)\b.*\b(call|request|endpoint)\b",
                    r"\b(receive|handle|process)\b.*\b(webhook|http request|api call)\b"
                ],
                "keywords": ["webhook", "http", "api", "trigger", "external", "endpoint"],
                "confidence_boost": 0.1
            },
            "api_call": {
                "patterns": [
                    r"\b(api|rest|http)\b.*\b(call|request|get|post)\b",
                    r"\b(call|request|fetch)\b.*\b(api|service|endpoint)\b"
                ],
                "keywords": ["api", "rest", "http", "call", "request", "service", "endpoint"],
                "confidence_boost": 0.1
            },
            "data_processor": {
                "patterns": [
                    r"\b(process|analyze|transform|clean)\b.*\b(data|information)\b",
                    r"\b(data|information)\b.*\b(processor|analyzer|transformer)\b"
                ],
                "keywords": ["process", "analyze", "transform", "clean", "data", "information"],
                "confidence_boost": 0.1
            }
        }

    def _initialize_workflow_templates(self) -> Dict[str, Dict]:
        """Initialize common workflow templates"""
        return {
            "email_automation": {
                "name": "Email Automation",
                "blocks": ["email_reader", "ai_text_generator", "email_sender"],
                "patterns": [
                    r"\b(email|mail)\b.*\b(automation|auto.?response|auto.?reply)\b",
                    r"\b(customer service|support)\b.*\b(email|automation)\b"
                ]
            },
            "social_media_management": {
                "name": "Social Media Management",
                "blocks": ["web_scraper", "ai_text_generator", "social_poster"],
                "patterns": [
                    r"\b(social media|social)\b.*\b(management|automation|posting)\b",
                    r"\b(twitter|facebook|linkedin)\b.*\b(automation|posting|management)\b"
                ]
            },
            "content_creation": {
                "name": "Content Creation Pipeline",
                "blocks": ["web_scraper", "ai_text_generator", "file_writer"],
                "patterns": [
                    r"\b(content|blog|article)\b.*\b(creation|generation|writing)\b",
                    r"\b(generate|create|write)\b.*\b(content|articles|blog posts)\b"
                ]
            },
            "data_reporting": {
                "name": "Data Reporting",
                "blocks": ["scheduler", "file_reader", "ai_text_generator", "email_sender"],
                "patterns": [
                    r"\b(report|analytics|dashboard)\b.*\b(automation|generation)\b",
                    r"\b(daily|weekly|monthly)\b.*\b(report|summary|analytics)\b"
                ]
            },
            "monitoring_alerts": {
                "name": "Monitoring & Alerts",
                "blocks": ["web_scraper", "conditional", "notification_sender"],
                "patterns": [
                    r"\b(monitor|watch|track)\b.*\b(alert|notification|notify)\b",
                    r"\b(alert|notification)\b.*\b(when|if|condition)\b"
                ]
            }
        }

    async def process_description(self, description: str) -> Dict[str, Any]:
        """Process natural language description and generate workflow configuration"""
        start_time = time.time()
        
        # Clean and normalize the description
        cleaned_description = self._clean_description(description)
        
        # Extract entities and analyze intent
        doc = self.nlp(cleaned_description)
        entities = self._extract_entities(doc)
        
        # Identify required blocks
        blocks = self._identify_blocks(cleaned_description, entities)
        
        # Determine workflow template
        template = self._identify_workflow_template(cleaned_description, blocks)
        
        # Generate connections between blocks
        connections = self._generate_connections(blocks, template)
        
        # Generate workflow configuration using AI
        ai_config = await self._generate_ai_configuration(cleaned_description, blocks, entities)
        
        # Calculate confidence score
        confidence = self._calculate_confidence(blocks, entities, template)
        
        # Generate suggested name and description
        suggested_name, suggested_description = self._generate_suggestions(cleaned_description, template, entities)
        
        processing_time = time.time() - start_time
        
        return {
            "workflow_config": {
                "settings": {
                    "auto_retry": True,
                    "timeout": 300,
                    "max_executions": 100
                },
                "connections": connections,
                "template": template["name"] if template else "Custom Workflow"
            },
            "blocks": blocks,
            "connections": connections,
            "confidence": confidence,
            "suggested_name": suggested_name,
            "suggested_description": suggested_description,
            "entities": entities,
            "processing_time": processing_time,
            "ai_config": ai_config
        }

    def _clean_description(self, description: str) -> str:
        """Clean and normalize the input description"""
        # Remove extra whitespace
        description = re.sub(r'\s+', ' ', description.strip())
        
        # Normalize common variations
        description = re.sub(r'\bauto\s+respond\b', 'auto-respond', description, flags=re.IGNORECASE)
        description = re.sub(r'\bauto\s+reply\b', 'auto-reply', description, flags=re.IGNORECASE)
        description = re.sub(r'\bemail\s+automation\b', 'email automation', description, flags=re.IGNORECASE)
        description = re.sub(r'\bsocial\s+media\b', 'social media', description, flags=re.IGNORECASE)
        
        return description.lower()

    def _extract_entities(self, doc) -> Dict[str, List[str]]:
        """Extract named entities and important terms"""
        entities = {
            "organizations": [],
            "services": [],
            "technologies": [],
            "frequencies": [],
            "actions": [],
            "data_types": []
        }
        
        # Extract named entities
        for ent in doc.ents:
            if ent.label_ in ["ORG", "PRODUCT"]:
                entities["organizations"].append(ent.text.lower())
        
        # Extract service-related terms
        service_terms = ["gmail", "twitter", "facebook", "linkedin", "instagram", "slack", "api", "webhook"]
        for token in doc:
            if token.text.lower() in service_terms:
                entities["services"].append(token.text.lower())
        
        # Extract frequency terms
        frequency_terms = ["daily", "weekly", "monthly", "hourly", "regularly", "continuously"]
        for token in doc:
            if token.text.lower() in frequency_terms:
                entities["frequencies"].append(token.text.lower())
        
        # Extract action verbs
        for token in doc:
            if token.pos_ == "VERB" and token.lemma_ in [
                "send", "read", "write", "generate", "create", "monitor", "check", "analyze", "process"
            ]:
                entities["actions"].append(token.lemma_)
        
        # Extract data types
        data_terms = ["email", "file", "csv", "excel", "pdf", "json", "data", "text", "content"]
        for token in doc:
            if token.text.lower() in data_terms:
                entities["data_types"].append(token.text.lower())
        
        return entities

    def _identify_blocks(self, description: str, entities: Dict[str, List[str]]) -> List[Dict[str, Any]]:
        """Identify required blocks based on description and entities"""
        identified_blocks = []
        block_scores = {}
        
        # Score each block type based on patterns and keywords
        for block_type, config in self.block_patterns.items():
            score = 0
            
            # Pattern matching
            for pattern in config["patterns"]:
                if re.search(pattern, description, re.IGNORECASE):
                    score += 0.3
            
            # Keyword matching
            for keyword in config["keywords"]:
                if keyword in description:
                    score += 0.1
            
            # Entity boosting
            if block_type == "email_reader" and "email" in entities["data_types"]:
                score += 0.2
            if block_type == "social_poster" and any(s in entities["services"] for s in ["twitter", "facebook", "linkedin"]):
                score += 0.2
            if block_type == "scheduler" and entities["frequencies"]:
                score += 0.2
            
            if score > 0.2:  # Threshold for inclusion
                block_scores[block_type] = score
        
        # Create block configurations
        for block_type, score in sorted(block_scores.items(), key=lambda x: x[1], reverse=True):
            block_config = self._create_block_config(block_type, entities, description)
            identified_blocks.append({
                "type": block_type,
                "name": block_config["name"],
                "config": block_config["config"],
                "confidence": min(score, 1.0),
                "position": self._generate_block_position(len(identified_blocks))
            })
        
        return identified_blocks

    def _create_block_config(self, block_type: str, entities: Dict[str, List[str]], description: str) -> Dict[str, Any]:
        """Create configuration for a specific block type"""
        configs = {
            "email_reader": {
                "name": "Email Reader",
                "config": {
                    "provider": "gmail" if "gmail" in entities["services"] else "email",
                    "folder": "inbox",
                    "max_emails": 10,
                    "filters": {
                        "unread": True
                    }
                }
            },
            "email_sender": {
                "name": "Email Sender",
                "config": {
                    "provider": "gmail" if "gmail" in entities["services"] else "email",
                    "reply_to_original": "reply" in description or "respond" in description
                }
            },
            "ai_text_generator": {
                "name": "AI Text Generator",
                "config": {
                    "model": "llama-4-scout-17b-16e-instruct",
                    "temperature": 0.7,
                    "max_tokens": 500,
                    "tone": "professional"
                }
            },
            "web_scraper": {
                "name": "Web Scraper",
                "config": {
                    "selectors": [".content", ".article", ".post"],
                    "wait_time": 2
                }
            },
            "social_poster": {
                "name": "Social Media Poster",
                "config": {
                    "platforms": [s for s in entities["services"] if s in ["twitter", "facebook", "linkedin"]],
                    "schedule": "immediate"
                }
            },
            "scheduler": {
                "name": "Scheduler",
                "config": {
                    "frequency": entities["frequencies"][0] if entities["frequencies"] else "daily",
                    "time": "09:00"
                }
            },
            "conditional": {
                "name": "Conditional Logic",
                "config": {
                    "operator": "contains",
                    "condition": "if condition"
                }
            },
            "file_reader": {
                "name": "File Reader",
                "config": {
                    "file_type": entities["data_types"][0] if entities["data_types"] else "txt"
                }
            },
            "file_writer": {
                "name": "File Writer",
                "config": {
                    "file_type": "txt",
                    "filename": "output.txt"
                }
            },
            "notification_sender": {
                "name": "Notification Sender",
                "config": {
                    "channel": "email",
                    "priority": "normal"
                }
            },
            "webhook": {
                "name": "Webhook",
                "config": {
                    "method": "POST",
                    "headers": {}
                }
            },
            "api_call": {
                "name": "API Call",
                "config": {
                    "method": "GET",
                    "headers": {"Content-Type": "application/json"}
                }
            },
            "data_processor": {
                "name": "Data Processor",
                "config": {
                    "operations": ["clean", "validate"]
                }
            }
        }
        
        return configs.get(block_type, {"name": "Unknown Block", "config": {}})

    def _generate_block_position(self, index: int) -> Dict[str, float]:
        """Generate position for block in canvas"""
        return {
            "x": 100 + (index * 200),
            "y": 100 + (index % 3) * 150
        }

    def _identify_workflow_template(self, description: str, blocks: List[Dict]) -> Dict[str, Any]:
        """Identify the most appropriate workflow template"""
        template_scores = {}
        
        for template_name, template_config in self.workflow_templates.items():
            score = 0
            
            # Pattern matching
            for pattern in template_config["patterns"]:
                if re.search(pattern, description, re.IGNORECASE):
                    score += 0.5
            
            # Block matching
            template_blocks = set(template_config["blocks"])
            identified_blocks = set(block["type"] for block in blocks)
            
            overlap = len(template_blocks.intersection(identified_blocks))
            total = len(template_blocks.union(identified_blocks))
            
            if total > 0:
                score += (overlap / total) * 0.5
            
            template_scores[template_name] = score
        
        if template_scores:
            best_template = max(template_scores, key=template_scores.get)
            if template_scores[best_template] > 0.3:
                return {
                    "name": self.workflow_templates[best_template]["name"],
                    "type": best_template,
                    "confidence": template_scores[best_template],
                    "blocks": self.workflow_templates[best_template]["blocks"]
                }
        
        return None

    def _generate_connections(self, blocks: List[Dict], template: Dict) -> List[Dict[str, Any]]:
        """Generate connections between blocks"""
        connections = []
        
        if len(blocks) < 2:
            return connections
        
        # If we have a template, use its connection pattern
        if template and "blocks" in template:
            template_blocks = template["blocks"]
            block_map = {block["type"]: i for i, block in enumerate(blocks)}
            
            for i, template_block in enumerate(template_blocks[:-1]):
                if template_block in block_map and template_blocks[i + 1] in block_map:
                    from_idx = block_map[template_block]
                    to_idx = block_map[template_blocks[i + 1]]
                    
                    connections.append({
                        "from_block": f"block_{from_idx}",
                        "to_block": f"block_{to_idx}",
                        "from_output": self._get_output_name(blocks[from_idx]["type"]),
                        "to_input": self._get_input_name(blocks[to_idx]["type"])
                    })
        else:
            # Create sequential connections
            for i in range(len(blocks) - 1):
                connections.append({
                    "from_block": f"block_{i}",
                    "to_block": f"block_{i + 1}",
                    "from_output": self._get_output_name(blocks[i]["type"]),
                    "to_input": self._get_input_name(blocks[i + 1]["type"])
                })
        
        return connections

    def _get_output_name(self, block_type: str) -> str:
        """Get the primary output name for a block type"""
        outputs = {
            "email_reader": "emails",
            "ai_text_generator": "generated_text",
            "web_scraper": "scraped_data",
            "file_reader": "file_content",
            "data_processor": "processed_data",
            "scheduler": "trigger",
            "conditional": "condition_result",
            "webhook": "webhook_data",
            "api_call": "response"
        }
        return outputs.get(block_type, "output")

    def _get_input_name(self, block_type: str) -> str:
        """Get the primary input name for a block type"""
        inputs = {
            "ai_text_generator": "context",
            "email_sender": "body",
            "social_poster": "content",
            "file_writer": "content",
            "notification_sender": "message",
            "conditional": "input_data",
            "data_processor": "data"
        }
        return inputs.get(block_type, "input")

    async def _generate_ai_configuration(self, description: str, blocks: List[Dict], entities: Dict) -> Dict[str, Any]:
        """Use AI to generate additional configuration details"""
        if not self.cerebras_provider.is_available():
            return {}
        
        try:
            prompt = f"""
Given this workflow description: "{description}"

And these identified blocks: {json.dumps([b["type"] for b in blocks], indent=2)}

Generate optimal configuration suggestions for this automation workflow. Focus on:

1. Specific settings for each block type
2. Error handling and retry logic
3. Performance optimizations
4. Security considerations

Respond with a JSON object containing configuration recommendations.
"""
            
            response = await self.cerebras_provider.generate_chat_response(
                messages=[{"role": "user", "content": prompt}],
                model="llama-4-scout-17b-16e-instruct",
                max_tokens=500,
                temperature=0.3
            )
            
            # Try to parse JSON from response
            content = response["content"]
            if "```json" in content:
                json_str = content.split("```json")[1].split("```")[0]
            else:
                json_str = content
            
            return json.loads(json_str)
            
        except Exception as e:
            print(f"AI configuration generation failed: {e}")
            return {}

    def _calculate_confidence(self, blocks: List[Dict], entities: Dict, template: Dict) -> float:
        """Calculate overall confidence score for the workflow generation"""
        if not blocks:
            return 0.0
        
        # Base confidence from block identification
        block_confidence = sum(block.get("confidence", 0.5) for block in blocks) / len(blocks)
        
        # Entity extraction confidence
        entity_confidence = 0.7 if any(entities.values()) else 0.3
        
        # Template matching confidence
        template_confidence = template.get("confidence", 0.5) if template else 0.4
        
        # Weighted average
        confidence = (
            block_confidence * 0.5 +
            entity_confidence * 0.3 +
            template_confidence * 0.2
        )
        
        return min(confidence, 0.95)  # Cap at 95%

    def _generate_suggestions(self, description: str, template: Dict, entities: Dict) -> Tuple[str, str]:
        """Generate suggested workflow name and description"""
        # Extract key terms for naming
        key_terms = []
        
        if entities["actions"]:
            key_terms.extend(entities["actions"][:2])
        
        if entities["services"]:
            key_terms.extend(entities["services"][:2])
        
        if entities["data_types"]:
            key_terms.extend(entities["data_types"][:1])
        
        # Generate name
        if template:
            suggested_name = template["name"]
        elif key_terms:
            suggested_name = " ".join(term.title() for term in key_terms[:3]) + " Automation"
        else:
            suggested_name = "Custom Automation Workflow"
        
        # Generate description
        if "email" in description and "auto" in description:
            suggested_description = "Automatically process and respond to emails using AI-powered responses"
        elif "social" in description:
            suggested_description = "Automate social media content creation and posting"
        elif "report" in description:
            suggested_description = "Generate automated reports with data analysis and insights"
        elif "monitor" in description:
            suggested_description = "Monitor external sources and trigger actions based on conditions"
        else:
            suggested_description = f"Automated workflow based on: {description[:100]}{'...' if len(description) > 100 else ''}"
        
        return suggested_name, suggested_description
