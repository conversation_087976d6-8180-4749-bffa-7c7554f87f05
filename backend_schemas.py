"""
Pydantic Schemas for Request/Response Validation
File: backend/schemas.py
"""

from pydantic import BaseModel, EmailStr, validator
from typing import Optional, Dict, Any, List
from datetime import datetime

# Authentication Schemas

class UserCreate(BaseModel):
    email: EmailStr
    name: str
    password: str

    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        return v

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class UserResponse(BaseModel):
    id: int
    email: str
    name: str
    is_active: bool = True
    is_premium: bool = False
    created_at: datetime

    class Config:
        from_attributes = True

class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"

# Workflow Schemas

class WorkflowCreate(BaseModel):
    name: str
    description: Optional[str] = None
    config: Optional[Dict[str, Any]] = None
    category: Optional[str] = "general"
    tags: Optional[List[str]] = None

class WorkflowUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    config: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None

class WorkflowResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    config: Optional[Dict[str, Any]]
    is_active: bool
    is_public: bool
    category: str
    tags: Optional[List[str]]
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Block Schemas

class BlockCreate(BaseModel):
    name: str
    type: str
    config: Optional[Dict[str, Any]] = None
    position_x: Optional[float] = 0
    position_y: Optional[float] = 0

class BlockUpdate(BaseModel):
    name: Optional[str] = None
    config: Optional[Dict[str, Any]] = None
    position_x: Optional[float] = None
    position_y: Optional[float] = None
    is_enabled: Optional[bool] = None

class BlockResponse(BaseModel):
    id: int
    workflow_id: int
    name: str
    type: str
    config: Optional[Dict[str, Any]]
    position_x: float
    position_y: float
    is_enabled: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Execution Schemas

class ExecutionResponse(BaseModel):
    id: int
    workflow_id: int
    status: str
    result: Optional[Dict[str, Any]]
    error: Optional[str]
    execution_time: Optional[float]
    started_at: datetime
    completed_at: Optional[datetime]

    class Config:
        from_attributes = True

# API Key Schemas

class ApiKeyCreate(BaseModel):
    name: str
    provider: str
    api_key: str

class ApiKeyResponse(BaseModel):
    id: int
    name: str
    provider: str
    is_active: bool
    last_used_at: Optional[datetime]
    usage_count: int
    created_at: datetime

    class Config:
        from_attributes = True

# NLP Processing Schemas

class NLPProcessRequest(BaseModel):
    description: str

    @validator('description')
    def validate_description(cls, v):
        if len(v.strip()) < 10:
            raise ValueError('Description must be at least 10 characters long')
        return v.strip()

class NLPProcessResponse(BaseModel):
    success: bool
    workflow_config: Dict[str, Any]
    confidence: float
    blocks: List[Dict[str, Any]]
    connections: List[Dict[str, Any]]
    suggested_name: str
    suggested_description: str
    processing_time: Optional[float] = None

# Workflow Template Schemas

class WorkflowTemplateResponse(BaseModel):
    id: int
    name: str
    description: str
    category: str
    template_data: Dict[str, Any]
    is_featured: bool
    usage_count: int
    created_at: datetime

    class Config:
        from_attributes = True

# Settings Schemas

class ProfileUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[EmailStr] = None
    current_password: Optional[str] = None
    new_password: Optional[str] = None

    @validator('new_password')
    def validate_new_password(cls, v):
        if v and len(v) < 8:
            raise ValueError('New password must be at least 8 characters long')
        return v

class NotificationSettings(BaseModel):
    workflow_completion: bool = True
    workflow_failures: bool = True
    weekly_reports: bool = False
    email_notifications: bool = True

# Health Check Schemas

class HealthResponse(BaseModel):
    status: str
    timestamp: datetime
    version: str
    services: Dict[str, str]

# AI Model Schemas

class AIModelInfo(BaseModel):
    id: str
    name: str
    provider: str
    context_length: int
    cost_per_token: float
    capabilities: List[str]

class AITestRequest(BaseModel):
    model: str
    prompt: str
    max_tokens: Optional[int] = 200
    temperature: Optional[float] = 0.7

class AITestResponse(BaseModel):
    success: bool
    content: str
    tokens_used: int
    response_time: float
    cost: float
    model: str

# Usage Tracking Schemas

class UsageRecord(BaseModel):
    action_type: str
    resource_used: str
    tokens_used: int
    cost: float
    timestamp: datetime

class UsageSummary(BaseModel):
    total_workflows: int
    total_executions: int
    total_tokens: int
    total_cost: float
    this_month_tokens: int
    this_month_cost: float

# Webhook Schemas

class WebhookCreate(BaseModel):
    name: str
    workflow_id: Optional[int] = None

class WebhookResponse(BaseModel):
    id: int
    name: str
    url_slug: str
    is_active: bool
    trigger_count: int
    created_at: datetime

    class Config:
        from_attributes = True

# Dashboard Schemas

class DashboardStats(BaseModel):
    total_workflows: int
    active_workflows: int
    total_executions: int
    successful_executions: int
    failed_executions: int
    total_tokens_used: int
    total_cost: float
    recent_executions: List[ExecutionResponse]

# Validation Schemas

class WorkflowValidationResult(BaseModel):
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    suggestions: List[str]

# Export Schemas

class ExportRequest(BaseModel):
    include_workflows: bool = True
    include_executions: bool = True
    include_settings: bool = False
    format: str = "json"  # json, csv
    date_range: Optional[Dict[str, str]] = None

class ExportResponse(BaseModel):
    download_url: str
    file_size: int
    expires_at: datetime

# Collaboration Schemas (for future team features)

class WorkflowShare(BaseModel):
    workflow_id: int
    email: EmailStr
    permission: str = "view"  # view, edit

class TeamMember(BaseModel):
    id: int
    name: str
    email: str
    role: str
    joined_at: datetime

    class Config:
        from_attributes = True
