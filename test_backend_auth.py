"""
Tests for Authentication Module
"""

import pytest
from datetime import datetime, timedelta
from backend_auth import (
    verify_password, 
    get_password_hash, 
    create_access_token, 
    verify_token
)

class TestPasswordHashing:
    """Test password hashing and verification"""
    
    def test_password_hashing(self):
        """Test password hashing works correctly"""
        password = "test_password_123"
        hashed = get_password_hash(password)
        
        # Hash should be different from original password
        assert hashed != password
        # Hash should be consistent
        assert len(hashed) > 0
        
    def test_password_verification_success(self):
        """Test password verification with correct password"""
        password = "test_password_123"
        hashed = get_password_hash(password)
        
        assert verify_password(password, hashed) is True
        
    def test_password_verification_failure(self):
        """Test password verification with incorrect password"""
        password = "test_password_123"
        wrong_password = "wrong_password"
        hashed = get_password_hash(password)
        
        assert verify_password(wrong_password, hashed) is False

class TestJWTTokens:
    """Test JWT token creation and verification"""
    
    def test_create_access_token(self):
        """Test JWT token creation"""
        data = {"user_id": 1, "email": "<EMAIL>"}
        token = create_access_token(data)
        
        assert isinstance(token, str)
        assert len(token) > 0
        
    def test_verify_valid_token(self):
        """Test verification of valid JWT token"""
        data = {"user_id": 1, "email": "<EMAIL>"}
        token = create_access_token(data)
        
        payload = verify_token(token)
        
        assert payload is not None
        assert payload["user_id"] == 1
        assert payload["email"] == "<EMAIL>"
        assert "exp" in payload
        assert "iat" in payload
        
    def test_verify_invalid_token(self):
        """Test verification of invalid JWT token"""
        invalid_token = "invalid.token.here"
        
        payload = verify_token(invalid_token)
        
        assert payload is None
        
    def test_create_token_with_custom_expiry(self):
        """Test JWT token creation with custom expiry"""
        data = {"user_id": 1}
        expires_delta = timedelta(minutes=30)
        token = create_access_token(data, expires_delta)
        
        payload = verify_token(token)
        
        assert payload is not None
        assert payload["user_id"] == 1
        
        # Check expiry is approximately correct (within 1 minute tolerance)
        exp_timestamp = payload["exp"]
        expected_exp = datetime.utcnow() + expires_delta
        actual_exp = datetime.fromtimestamp(exp_timestamp)
        
        time_diff = abs((expected_exp - actual_exp).total_seconds())
        assert time_diff < 60  # Within 1 minute tolerance

class TestTokenSecurity:
    """Test token security features"""
    
    def test_tokens_are_unique(self):
        """Test that tokens generated for same data are unique"""
        data = {"user_id": 1}
        token1 = create_access_token(data)
        token2 = create_access_token(data)
        
        # Tokens should be different due to different iat (issued at) times
        assert token1 != token2
        
    def test_token_contains_required_claims(self):
        """Test that tokens contain required claims"""
        data = {"user_id": 1, "email": "<EMAIL>"}
        token = create_access_token(data)
        payload = verify_token(token)
        
        # Check required claims
        assert "exp" in payload  # Expiration time
        assert "iat" in payload  # Issued at time
        assert "user_id" in payload  # User data
        assert "email" in payload  # User data

if __name__ == "__main__":
    pytest.main([__file__])
