import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from './frontend_auth_context_fixed';

const Login = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const from = location.state?.from?.pathname || '/dashboard';

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    // Clear error when user starts typing
    if (error) setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const result = await login(formData.email, formData.password);
      
      if (result.success) {
        navigate(from, { replace: true });
      } else {
        setError(result.error || 'Login failed');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDemoLogin = async () => {
    setFormData({
      email: '<EMAIL>',
      password: 'password123'
    });
    
    setIsLoading(true);
    const result = await login('<EMAIL>', 'password123');
    
    if (result.success) {
      navigate(from, { replace: true });
    } else {
      setError(result.error || 'Demo login failed');
    }
    setIsLoading(false);
  };

  return (
    <div className="auth-page">
      <div className="auth-container">
        <div className="auth-card">
          <div className="auth-header">
            <h1>Welcome Back</h1>
            <p>Sign in to your AutoGPT account</p>
          </div>

          <form onSubmit={handleSubmit} className="auth-form">
            {error && (
              <div className="error-message">
                {error}
              </div>
            )}

            <div className="form-group">
              <label htmlFor="email">Email Address</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                placeholder="Enter your email"
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label htmlFor="password">Password</label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                required
                placeholder="Enter your password"
                className="form-input"
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="btn btn-primary btn-full"
            >
              {isLoading ? 'Signing In...' : 'Sign In'}
            </button>
          </form>

          <div className="auth-divider">
            <span>or</span>
          </div>

          <button
            onClick={handleDemoLogin}
            disabled={isLoading}
            className="btn btn-secondary btn-full"
          >
            Try Demo Account
          </button>

          <div className="auth-footer">
            <p>
              Don't have an account?{' '}
              <Link to="/register" className="auth-link">
                Sign up
              </Link>
            </p>
          </div>
        </div>

        <div className="auth-side">
          <div className="auth-visual">
            <h2>Automate Everything</h2>
            <p>
              Join thousands of users who are already saving time 
              with intelligent automation workflows.
            </p>
            
            <div className="features-list">
              <div className="feature-item">
                <span className="feature-icon">✅</span>
                Ultra-fast AI processing
              </div>
              <div className="feature-item">
                <span className="feature-icon">✅</span>
                Natural language interface
              </div>
              <div className="feature-item">
                <span className="feature-icon">✅</span>
                Visual workflow builder
              </div>
              <div className="feature-item">
                <span className="feature-icon">✅</span>
                Real-time analytics
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
