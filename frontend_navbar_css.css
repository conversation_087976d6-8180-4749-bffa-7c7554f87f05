/* frontend/src/components/Navbar.css */

.navbar {
position: fixed;
top: 0;
left: 0;
right: 0;
background-color: white;
border-bottom: 1px solid var(–gray-200);
z-index: 1000;
box-shadow: var(–shadow-sm);
}

.nav-container {
max-width: 1400px;
margin: 0 auto;
padding: 0 1rem;
display: flex;
align-items: center;
justify-content: space-between;
height: 4rem;
}

/* Logo */
.nav-logo {
display: flex;
align-items: center;
gap: 0.5rem;
text-decoration: none;
color: var(–gray-900);
font-weight: 700;
font-size: 1.25rem;
}

.logo-icon {
font-size: 1.5rem;
}

.logo-text {
background: linear-gradient(135deg, var(–primary-600), #8b5cf6);
-webkit-background-clip: text;
-webkit-text-fill-color: transparent;
background-clip: text;
}

/* Desktop Navigation */
.desktop-nav {
display: flex;
align-items: center;
gap: 0.5rem;
}

.nav-link {
display: flex;
align-items: center;
gap: 0.5rem;
padding: 0.5rem 1rem;
border-radius: var(–radius-md);
text-decoration: none;
color: var(–gray-600);
font-weight: 500;
font-size: 0.875rem;
transition: all var(–transition-fast);
position: relative;
}

.nav-link:hover {
color: var(–primary-600);
background-color: var(–primary-50);
}

.nav-link.active {
color: var(–primary-600);
background-color: var(–primary-50);
}

.nav-link.active::after {
content: ‘’;
position: absolute;
bottom: -12px;
left: 50%;
transform: translateX(-50%);
width: 4px;
height: 4px;
background-color: var(–primary-600);
border-radius: 50%;
}

.nav-icon {
font-size: 1rem;
}

.nav-label {
white-space: nowrap;
}

/* User Menu */
.user-menu {
position: relative;
}

.user-button {
display: flex;
align-items: center;
gap: 0.75rem;
padding: 0.5rem;
border: none;
background: none;
cursor: pointer;
border-radius: var(–radius-md);
transition: background-color var(–transition-fast);
}

.user-button:hover {
background-color: var(–gray-50);
}

.user-avatar {
width: 2rem;
height: 2rem;
border-radius: 50%;
background: linear-gradient(135deg, var(–primary-500), #8b5cf6);
display: flex;
align-items: center;
justify-content: center;
color: white;
font-weight: 600;
font-size: 0.875rem;
}

.user-name {
font-weight: 500;
color: var(–gray-700);
font-size: 0.875rem;
}

.dropdown-arrow {
color: var(–gray-400);
font-size: 0.75rem;
transition: transform var(–transition-fast);
}

.user-button:hover .dropdown-arrow {
transform: rotate(180deg);
}

/* User Dropdown */
.user-dropdown {
position: absolute;
top: 100%;
right: 0;
margin-top: 0.5rem;
background-color: white;
border: 1px solid var(–gray-200);
border-radius: var(–radius-lg);
box-shadow: var(–shadow-lg);
min-width: 200px;
overflow: hidden;
z-index: 50;
animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
from {
opacity: 0;
transform: translateY(-10px);
}
to {
opacity: 1;
transform: translateY(0);
}
}

.dropdown-header {
padding: 1rem;
border-bottom: 1px solid var(–gray-100);
}

.user-info .user-name {
font-weight: 600;
color: var(–gray-900);
margin-bottom: 0.25rem;
}

.user-info .user-email {
font-size: 0.75rem;
color: var(–gray-500);
}

.dropdown-divider {
height: 1px;
background-color: var(–gray-100);
}

.dropdown-item {
display: flex;
align-items: center;
gap: 0.75rem;
width: 100%;
padding: 0.75rem 1rem;
border: none;
background: none;
text-decoration: none;
color: var(–gray-700);
font-size: 0.875rem;
cursor: pointer;
transition: background-color var(–transition-fast);
}

.dropdown-item:hover {
background-color: var(–gray-50);
}

.dropdown-item.logout-item {
color: var(–error-500);
}

.dropdown-item.logout-item:hover {
background-color: #fef2f2;
}

.item-icon {
font-size: 1rem;
}

/* Auth Links */
.auth-links {
display: flex;
align-items: center;
gap: 1rem;
}

.nav-button {
display: inline-flex;
align-items: center;
gap: 0.5rem;
padding: 0.5rem 1rem;
background-color: var(–primary-600);
color: white;
text-decoration: none;
border-radius: var(–radius-md);
font-weight: 500;
font-size: 0.875rem;
transition: all var(–transition-fast);
}

.nav-button:hover {
background-color: var(–primary-700);
transform: translateY(-1px);
box-shadow: var(–shadow-md);
}

/* Mobile Menu */
.mobile-menu-toggle {
display: none;
flex-direction: column;
gap: 0.25rem;
background: none;
border: none;
cursor: pointer;
padding: 0.5rem;
}

.mobile-menu-toggle span {
width: 1.5rem;
height: 2px;
background-color: var(–gray-600);
transition: all var(–transition-fast);
}

.mobile-nav {
position: fixed;
top: 4rem;
left: 0;
right: 0;
background-color: white;
border-bottom: 1px solid var(–gray-200);
box-shadow: var(–shadow-lg);
padding: 1rem;
z-index: 40;
animation: slideDown 0.2s ease-out;
}

.mobile-nav-link {
display: flex;
align-items: center;
gap: 0.75rem;
padding: 0.75rem;
text-decoration: none;
color: var(–gray-700);
font-weight: 500;
border-radius: var(–radius-md);
transition: all var(–transition-fast);
margin-bottom: 0.25rem;
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
background-color: var(–primary-50);
color: var(–primary-600);
}

.mobile-nav-link.logout-link {
color: var(–error-500);
border: none;
background: none;
width: 100%;
text-align: left;
cursor: pointer;
}

.mobile-nav-link.logout-link:hover {
background-color: #fef2f2;
}

.mobile-nav-divider {
height: 1px;
background-color: var(–gray-200);
margin: 0.75rem 0;
}

/* Overlay */
.nav-overlay {
position: fixed;
top: 0;
left: 0;
right: 0;
bottom: 0;
background-color: rgba(0, 0, 0, 0.1);
z-index: 30;
}

/* Responsive Design */
@media (max-width: 768px) {
.desktop-nav {
display: none;
}

.mobile-menu-toggle {
display: flex;
}

.user-name {
display: none;
}

.nav-container {
padding: 0 1rem;
}
}

@media (max-width: 480px) {
.logo-text {
display: none;
}

.nav-container {
height: 3.5rem;
}

.mobile-nav {
top: 3.5rem;
}
}

/* Focus States */
.nav-link:focus-visible,
.user-button:focus-visible,
.dropdown-item:focus-visible,
.mobile-nav-link:focus-visible {
outline: 2px solid var(–primary-500);
outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
.navbar {
border-bottom-width: 2px;
}

.nav-link.active::after {
width: 6px;
height: 6px;
}
}