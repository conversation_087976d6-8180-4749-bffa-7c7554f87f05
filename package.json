{"name": "autogpt-frontend", "version": "1.0.0", "description": "AutoGPT Frontend - React Application", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "axios": "^1.6.2", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwindcss": "^3.3.6", "@headlessui/react": "^1.7.17", "framer-motion": "^10.16.16", "recharts": "^2.8.0", "react-flow-renderer": "^10.3.17", "web-vitals": "^3.5.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,json,css,md}"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}, "proxy": "http://localhost:8000"}