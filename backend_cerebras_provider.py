"""
Cerebras AI Provider for Ultra-Fast Inference
File: backend/cerebras_provider.py
"""

import os
import time
import asyncio
import httpx
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)

class CerebrasProvider:
    """Provides ultra-fast AI inference using Cerebras Cloud SDK"""

    def __init__(self):
        self.api_key = os.getenv("CEREBRAS_API_KEY")
        self.base_url = "https://api.cerebras.ai/v1"
        self.client = None
        self.available_models = {
            "llama-4-scout-17b-16e-instruct": {
                "name": "Llama 4 Scout 17B",
                "context_length": 128000,
                "cost_per_token": 0.0004,
                "speed": "2600+ tokens/sec",
                "capabilities": ["chat", "completion", "reasoning"]
            },
            "llama-3.1-70b-instruct": {
                "name": "Llama 3.1 70B Instruct",
                "context_length": 128000,
                "cost_per_token": 0.0006,
                "speed": "1800+ tokens/sec",
                "capabilities": ["chat", "completion", "reasoning"]
            },
            "llama-3.1-8b-instruct": {
                "name": "Llama 3.1 8B Instruct",
                "context_length": 128000,
                "cost_per_token": 0.0002,
                "speed": "3000+ tokens/sec",
                "capabilities": ["chat", "completion"]
            },
            "qwen-3-32b-instruct": {
                "name": "Qwen 3 32B Instruct",
                "context_length": 32768,
                "cost_per_token": 0.0003,
                "speed": "2200+ tokens/sec",
                "capabilities": ["chat", "completion", "multilingual"]
            }
        }
        self._initialize_client()

    def _initialize_client(self):
        """Initialize the HTTP client for Cerebras API"""
        if not self.api_key:
            logger.warning("⚠️ Cerebras API key not found. AI features will be limited.")
            return
        
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            },
            timeout=120.0  # 2 minutes timeout
        )
        logger.info("✅ Cerebras client initialized successfully")

    def is_available(self) -> bool:
        """Check if Cerebras provider is available"""
        return self.api_key is not None and self.client is not None

    async def get_available_models(self) -> List[Dict[str, Any]]:
        """Get list of available models"""
        return [
            {
                "id": model_id,
                **model_info
            }
            for model_id, model_info in self.available_models.items()
        ]

    async def generate_chat_response(
        self,
        messages: List[Dict[str, str]],
        model: str = "llama-4-scout-17b-16e-instruct",
        max_tokens: int = 1000,
        temperature: float = 0.7,
        top_p: float = 0.95,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Generate chat response using Cerebras"""
        
        if not self.is_available():
            # Mock response for demo when API key is not available
            return self._mock_response(messages, model, max_tokens)
        
        start_time = time.time()
        
        try:
            payload = {
                "model": model,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "top_p": top_p,
                "stream": stream,
                "stop": None
            }
            
            response = await self.client.post("/chat/completions", json=payload)
            response.raise_for_status()
            
            data = response.json()
            
            # Extract response details
            choice = data["choices"][0]
            content = choice["message"]["content"]
            finish_reason = choice["finish_reason"]
            
            # Calculate metrics
            response_time = time.time() - start_time
            usage = data.get("usage", {})
            prompt_tokens = usage.get("prompt_tokens", 0)
            completion_tokens = usage.get("completion_tokens", 0)
            total_tokens = usage.get("total_tokens", prompt_tokens + completion_tokens)
            
            # Calculate cost and performance metrics
            model_info = self.available_models.get(model, {})
            cost_per_token = model_info.get("cost_per_token", 0.0004)
            cost = total_tokens * cost_per_token
            
            tokens_per_second = completion_tokens / response_time if response_time > 0 else 0
            
            return {
                "content": content,
                "finish_reason": finish_reason,
                "model": model,
                "usage": {
                    "prompt_tokens": prompt_tokens,
                    "completion_tokens": completion_tokens,
                    "total_tokens": total_tokens
                },
                "performance": {
                    "response_time": response_time,
                    "tokens_per_second": tokens_per_second,
                    "cost": cost,
                    "cost_per_token": cost_per_token
                },
                "timestamp": time.time()
            }
            
        except httpx.HTTPStatusError as e:
            logger.error(f"Cerebras API HTTP error: {e.response.status_code} - {e.response.text}")
            raise Exception(f"Cerebras API error: {e.response.status_code}")
        
        except httpx.RequestError as e:
            logger.error(f"Cerebras API request error: {e}")
            raise Exception(f"Cerebras API request failed: {str(e)}")
        
        except Exception as e:
            logger.error(f"Unexpected error in Cerebras API call: {e}")
            raise Exception(f"Cerebras API call failed: {str(e)}")

    async def generate_completion(
        self,
        prompt: str,
        model: str = "llama-4-scout-17b-16e-instruct",
        max_tokens: int = 1000,
        temperature: float = 0.7,
        top_p: float = 0.95
    ) -> Dict[str, Any]:
        """Generate text completion using Cerebras"""
        
        # Convert to chat format
        messages = [{"role": "user", "content": prompt}]
        return await self.generate_chat_response(
            messages=messages,
            model=model,
            max_tokens=max_tokens,
            temperature=temperature,
            top_p=top_p
        )

    async def generate_streaming_response(
        self,
        messages: List[Dict[str, str]],
        model: str = "llama-4-scout-17b-16e-instruct",
        max_tokens: int = 1000,
        temperature: float = 0.7
    ):
        """Generate streaming chat response"""
        
        if not self.is_available():
            # Mock streaming response
            content = "This is a mock streaming response when Cerebras API is not available."
            for word in content.split():
                yield {
                    "content": word + " ",
                    "done": False
                }
                await asyncio.sleep(0.1)
            yield {"content": "", "done": True}
            return
        
        try:
            payload = {
                "model": model,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": True
            }
            
            async with self.client.stream("POST", "/chat/completions", json=payload) as response:
                response.raise_for_status()
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]  # Remove "data: " prefix
                        
                        if data_str == "[DONE]":
                            yield {"content": "", "done": True}
                            break
                        
                        try:
                            import json
                            data = json.loads(data_str)
                            
                            if "choices" in data and data["choices"]:
                                delta = data["choices"][0].get("delta", {})
                                content = delta.get("content", "")
                                
                                if content:
                                    yield {
                                        "content": content,
                                        "done": False
                                    }
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            logger.error(f"Streaming response error: {e}")
            yield {
                "content": f"Error: {str(e)}",
                "done": True
            }

    def _mock_response(
        self,
        messages: List[Dict[str, str]],
        model: str,
        max_tokens: int
    ) -> Dict[str, Any]:
        """Generate mock response when API is not available"""
        
        # Simulate processing time
        time.sleep(0.5)
        
        # Generate mock content based on the last message
        last_message = messages[-1]["content"] if messages else ""
        
        if "email" in last_message.lower():
            mock_content = """Dear [Customer Name],

Thank you for reaching out to us. We appreciate your inquiry and are here to help.

Based on your message, I understand you're looking for information about our services. I'd be happy to provide you with the details you need.

Please let me know if you have any specific questions, and I'll make sure to address them promptly.

Best regards,
Customer Service Team"""

        elif "social media" in last_message.lower() or "post" in last_message.lower():
            mock_content = """🚀 Exciting news! We're always innovating to bring you the best experience possible.

✨ Here's what's trending in our space:
• Automation that saves time
• AI-powered solutions
• Seamless integrations

What are you most excited about? Let us know in the comments!

#Innovation #AI #Automation #TechTrends"""

        elif "report" in last_message.lower() or "analysis" in last_message.lower():
            mock_content = """📊 WEEKLY PERFORMANCE REPORT

Key Metrics Summary:
• Total Workflows: 145 (+12% from last week)
• Successful Executions: 1,237 (96.8% success rate)
• Average Response Time: 1.2 seconds
• Cost Savings: $2,450 this week

Top Performing Automations:

1. Email Auto-Responder (87% efficiency)
2. Social Media Manager (82% engagement boost)
3. Data Report Generator (75% time saved)

Recommendations:
• Optimize webhook configurations for better performance
• Consider scaling successful workflows
• Review failed executions for improvement opportunities

Generated automatically by AutoGPT Analytics Engine"""

        else:
            mock_content = f"""I understand you're looking for assistance with: "{last_message[:100]}{'...' if len(last_message) > 100 else ''}"

Here's my analysis and recommendations:

1. **Assessment**: Based on your requirements, this looks like a great automation opportunity.
2. **Approach**: I recommend breaking this down into clear, actionable steps using our block-based workflow system.
3. **Implementation**: We can start with a simple automation and gradually add more sophistication.
4. **Benefits**: This automation could save significant time and improve consistency.

Would you like me to help you design a specific workflow for this use case?"""

        # Calculate mock metrics
        prompt_tokens = len(last_message.split()) * 1.3  # Rough estimation
        completion_tokens = len(mock_content.split()) * 1.3
        total_tokens = int(prompt_tokens + completion_tokens)
        
        model_info = self.available_models.get(model, {})
        cost_per_token = model_info.get("cost_per_token", 0.0004)
        cost = total_tokens * cost_per_token
        
        return {
            "content": mock_content,
            "finish_reason": "stop",
            "model": model,
            "usage": {
                "prompt_tokens": int(prompt_tokens),
                "completion_tokens": int(completion_tokens),
                "total_tokens": total_tokens
            },
            "performance": {
                "response_time": 0.5,
                "tokens_per_second": int(completion_tokens / 0.5),
                "cost": cost,
                "cost_per_token": cost_per_token
            },
            "timestamp": time.time(),
            "mock": True  # Indicates this is a mock response
        }

    async def test_connection(self) -> Dict[str, Any]:
        """Test connection to Cerebras API"""
        if not self.is_available():
            return {
                "status": "unavailable",
                "message": "Cerebras API key not configured",
                "timestamp": time.time()
            }
        
        try:
            # Simple test request
            response = await self.generate_chat_response(
                messages=[{"role": "user", "content": "Hello, this is a connection test."}],
                model="llama-4-scout-17b-16e-instruct",
                max_tokens=50,
                temperature=0.1
            )
            
            return {
                "status": "connected",
                "message": "Cerebras API connection successful",
                "response_time": response["performance"]["response_time"],
                "tokens_per_second": response["performance"]["tokens_per_second"],
                "model": response["model"],
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Cerebras connection test failed: {e}")
            return {
                "status": "error",
                "message": f"Connection test failed: {str(e)}",
                "timestamp": time.time()
            }

    async def get_usage_stats(self) -> Dict[str, Any]:
        """Get usage statistics (mock implementation)"""
        # In a real implementation, this would fetch actual usage data
        return {
            "total_requests": 1547,
            "total_tokens": 2845691,
            "total_cost": 1138.28,
            "avg_response_time": 1.2,
            "success_rate": 98.7,
            "most_used_model": "llama-4-scout-17b-16e-instruct",
            "this_month": {
                "requests": 342,
                "tokens": 645123,
                "cost": 258.05
            },
            "last_updated": time.time()
        }

    async def close(self):
        """Close the HTTP client"""
        if self.client:
            await self.client.aclose()
            logger.info("Cerebras client closed")

    def get_model_info(self, model_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific model"""
        return self.available_models.get(model_id)

    def estimate_cost(self, prompt_length: int, max_tokens: int, model: str = "llama-4-scout-17b-16e-instruct") -> float:
        """Estimate the cost of a request"""
        model_info = self.available_models.get(model, {})
        cost_per_token = model_info.get("cost_per_token", 0.0004)
        
        # Rough estimation: prompt_length + max_tokens
        estimated_tokens = prompt_length + max_tokens
        return estimated_tokens * cost_per_token
