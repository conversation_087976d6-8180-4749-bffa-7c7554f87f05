// frontend/src/pages/Dashboard.js
import React, { useState, useEffect } from ‘react’;
import { Link } from ‘react-router-dom’;
import { useAuth } from ‘../contexts/AuthContext’;
import { useWorkflow } from ‘../contexts/WorkflowContext’;
import LoadingSpinner from ‘../components/LoadingSpinner’;
import ‘./Dashboard.css’;

const Dashboard = () => {
const { user } = useAuth();
const { workflows, fetchWorkflows, loading } = useWorkflow();
const [stats, setStats] = useState({
totalWorkflows: 0,
activeWorkflows: 0,
totalExecutions: 0,
successRate: 0
});

useEffect(() => {
fetchWorkflows();
}, [fetchWorkflows]);

useEffect(() => {
if (workflows.length > 0) {
// Calculate stats from workflows
const activeCount = workflows.filter(w => w.is_active).length;
const totalExecutions = workflows.reduce((sum, w) => sum + (w.execution_count || 0), 0);
const successfulExecutions = workflows.reduce((sum, w) => sum + (w.successful_executions || 0), 0);

```
  setStats({
    totalWorkflows: workflows.length,
    activeWorkflows: activeCount,
    totalExecutions: totalExecutions,
    successRate: totalExecutions > 0 ? ((successfulExecutions / totalExecutions) * 100).toFixed(1) : 0
  });
}
```

}, [workflows]);

const recentWorkflows = workflows
.sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at))
.slice(0, 4);

const quickActions = [
{
title: ‘NLP Builder’,
description: ‘Describe your automation in plain English’,
icon: ‘🧠’,
path: ‘/nlp-builder’,
color: ‘#8b5cf6’
},
{
title: ‘Visual Builder’,
description: ‘Create workflows with drag-and-drop blocks’,
icon: ‘🔧’,
path: ‘/workflows’,
color: ‘#3b82f6’
},
{
title: ‘Templates’,
description: ‘Start with pre-built automation templates’,
icon: ‘📝’,
path: ‘/templates’,
color: ‘#10b981’
},
{
title: ‘Import’,
description: ‘Import workflows from files or URLs’,
icon: ‘📥’,
path: ‘/import’,
color: ‘#f59e0b’
}
];

if (loading && workflows.length === 0) {
return <LoadingSpinner message="Loading dashboard..." />;
}

return (
<div className="dashboard">
<div className="dashboard-container">
{/* Header */}
<div className="dashboard-header">
<div className="header-content">
<h1>Welcome back, {user?.name || ‘User’}! 👋</h1>
<p>Manage your AI automations and monitor their performance</p>
</div>
<div className="header-actions">
<Link to="/nlp-builder" className="btn-primary">
<span className="btn-icon">🧠</span>
Create with NLP
</Link>
</div>
</div>

```
    {/* Stats Grid */}
    <div className="stats-grid">
      <div className="stat-card">
        <div className="stat-icon">🔧</div>
        <div className="stat-content">
          <div className="stat-number">{stats.totalWorkflows}</div>
          <div className="stat-label">Total Workflows</div>
        </div>
      </div>
      
      <div className="stat-card">
        <div className="stat-icon">⚡</div>
        <div className="stat-content">
          <div className="stat-number">{stats.activeWorkflows}</div>
          <div className="stat-label">Active Workflows</div>
        </div>
      </div>
      
      <div className="stat-card">
        <div className="stat-icon">📊</div>
        <div className="stat-content">
          <div className="stat-number">{stats.totalExecutions}</div>
          <div className="stat-label">Total Executions</div>
        </div>
      </div>
      
      <div className="stat-card">
        <div className="stat-icon">✅</div>
        <div className="stat-content">
          <div className="stat-number">{stats.successRate}%</div>
          <div className="stat-label">Success Rate</div>
        </div>
      </div>
    </div>

    {/* Quick Actions */}
    <div className="dashboard-section">
      <div className="section-header">
        <h2>Quick Actions</h2>
        <p>Get started with building your automations</p>
      </div>
      
      <div className="quick-actions-grid">
        {quickActions.map((action, index) => (
          <Link
            key={index}
            to={action.path}
            className="quick-action-card"
            style={{ '--accent-color': action.color }}
          >
            <div className="action-icon">{action.icon}</div>
            <div className="action-content">
              <h3>{action.title}</h3>
              <p>{action.description}</p>
            </div>
            <div className="action-arrow">→</div>
          </Link>
        ))}
      </div>
    </div>

    {/* Recent Workflows */}
    <div className="dashboard-section">
      <div className="section-header">
        <h2>Recent Workflows</h2>
        <Link to="/workflows" className="section-link">View all</Link>
      </div>
      
      {recentWorkflows.length > 0 ? (
        <div className="workflows-grid">
          {recentWorkflows.map((workflow) => (
            <Link
              key={workflow.id}
              to={`/workflows/${workflow.id}`}
              className="workflow-card"
            >
              <div className="workflow-header">
                <div className="workflow-icon">
                  {workflow.category === 'communication' ? '📧' :
                   workflow.category === 'social_media' ? '📱' :
                   workflow.category === 'data' ? '📊' :
                   workflow.category === 'ai_generated' ? '🧠' : '🔧'}
                </div>
                <div className="workflow-status">
                  <span className={`status-indicator ${workflow.is_active ? 'active' : 'inactive'}`}>
                    {workflow.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>
              
              <div className="workflow-content">
                <h3>{workflow.name}</h3>
                <p>{workflow.description}</p>
              </div>
              
              <div className="workflow-meta">
                <div className="meta-item">
                  <span className="meta-icon">🔗</span>
                  <span>{workflow.blocks?.length || 0} blocks</span>
                </div>
                <div className="meta-item">
                  <span className="meta-icon">📅</span>
                  <span>{new Date(workflow.updated_at).toLocaleDateString()}</span>
                </div>
              </div>
            </Link>
          ))}
        </div>
      ) : (
        <div className="empty-state">
          <div className="empty-icon">🚀</div>
          <h3>No workflows yet</h3>
          <p>Create your first automation to get started</p>
          <Link to="/nlp-builder" className="btn-primary">
            <span className="btn-icon">🧠</span>
            Create Your First Workflow
          </Link>
        </div>
      )}
    </div>

    {/* Getting Started */}
    {workflows.length === 0 && (
      <div className="dashboard-section">
        <div className="section-header">
          <h2>Getting Started</h2>
          <p>Learn how to build powerful automations with AutoGPT</p>
        </div>
        
        <div className="getting-started-grid">
          <div className="guide-card">
            <div className="guide-icon">📚</div>
            <div className="guide-content">
              <h3>Quick Tutorial</h3>
              <p>Learn the basics of creating automations with our interactive tutorial</p>
              <button className="guide-button">Start Tutorial</button>
            </div>
          </div>
          
          <div className="guide-card">
            <div className="guide-icon">🎥</div>
            <div className="guide-content">
              <h3>Video Guide</h3>
              <p>Watch how to create your first email automation in under 5 minutes</p>
              <button className="guide-button">Watch Video</button>
            </div>
          </div>
          
          <div className="guide-card">
            <div className="guide-icon">💬</div>
            <div className="guide-content">
              <h3>Examples</h3>
              <p>Try these example descriptions to see AutoGPT in action</p>
              <button className="guide-button">View Examples</button>
            </div>
          </div>
        </div>
      </div>
    )}

    {/* Recent Activity */}
    <div className="dashboard-section">
      <div className="section-header">
        <h2>Recent Activity</h2>
        <Link to="/analytics" className="section-link">View analytics</Link>
      </div>
      
      <div className="activity-list">
        <div className="activity-item">
          <div className="activity-icon">✅</div>
          <div className="activity-content">
            <div className="activity-title">Workflow "Email Auto-Responder" executed successfully</div>
            <div className="activity-time">2 minutes ago</div>
          </div>
        </div>
        
        <div className="activity-item">
          <div className="activity-icon">🔧</div>
          <div className="activity-content">
            <div className="activity-title">Created new workflow "Social Media Manager"</div>
            <div className="activity-time">1 hour ago</div>
          </div>
        </div>
        
        <div className="activity-item">
          <div className="activity-icon">📊</div>
          <div className="activity-content">
            <div className="activity-title">Generated weekly performance report</div>
            <div className="activity-time">1 day ago</div>
          </div>
        </div>
      </div>
    </div>

    {/* Performance Highlights */}
    <div className="dashboard-section">
      <div className="section-header">
        <h2>Performance Highlights</h2>
      </div>
      
      <div className="performance-grid">
        <div className="performance-card">
          <div className="performance-metric">
            <div className="metric-value">2,847</div>
            <div className="metric-label">Tokens Generated</div>
          </div>
          <div className="metric-change positive">
            <span>↗</span> +12% from last week
          </div>
        </div>
        
        <div className="performance-card">
          <div className="performance-metric">
            <div className="metric-value">1.2s</div>
            <div className="metric-label">Avg Response Time</div>
          </div>
          <div className="metric-change positive">
            <span>↗</span> 15% faster
          </div>
        </div>
        
        <div className="performance-card">
          <div className="performance-metric">
            <div className="metric-value">$2.45</div>
            <div className="metric-label">This Week's Cost</div>
          </div>
          <div className="metric-change negative">
            <span>↘</span> 8% reduction
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
```

);
};

export default Dashboard;