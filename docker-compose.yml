version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: autogpt-postgres
    environment:
      POSTGRES_DB: autogpt
      POSTGRES_USER: autogpt
      POSTGRES_PASSWORD: autogpt123
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U autogpt -d autogpt"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and rate limiting
  redis:
    image: redis:7-alpine
    container_name: autogpt-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: autogpt-backend
    environment:
      - DATABASE_URL=*********************************************/autogpt
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-your-super-secret-key-change-in-production}
      - CEREBRAS_API_KEY=${CEREBRAS_API_KEY}
      - CORS_ORIGINS=http://localhost:3000,http://frontend:3000
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Frontend React App
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: autogpt-frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000
    ports:
      - "3000:3000"
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  default:
    name: autogpt-network
