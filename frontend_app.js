import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Contexts
import { AuthProvider } from './frontend_auth_context_fixed';
import { WorkflowProvider } from './frontend_workflow_context_fixed';

// Components
import Navbar from './frontend_navbar_fixed';
import ProtectedRoute from './frontend_protected_route_fixed';

// Pages
import Landing from './frontend_landing_page_fixed';
import Login from './frontend_login_page_fixed';
import Register from './frontend_register_page_fixed';
import Dashboard from './frontend_dashboard_page_fixed';
import Workflows from './frontend_workflows_page_fixed';
import NLPBuilder from './frontend_nlp_builder_page_fixed';
import Analytics from './frontend_analytics_page_fixed';

function App() {
  return (
    <AuthProvider>
      <WorkflowProvider>
        <Router>
          <div className="App">
            <Navbar />
            <main className="main-content">
              <Routes>
                {/* Public Routes */}
                <Route path="/" element={<Landing />} />
                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />

                {/* Protected Routes */}
                <Route 
                  path="/dashboard" 
                  element={
                    <ProtectedRoute>
                      <Dashboard />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/workflows" 
                  element={
                    <ProtectedRoute>
                      <Workflows />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/nlp-builder" 
                  element={
                    <ProtectedRoute>
                      <NLPBuilder />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/analytics" 
                  element={
                    <ProtectedRoute>
                      <Analytics />
                    </ProtectedRoute>
                  } 
                />
                
                {/* Catch all route */}
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </main>
            
            {/* Toast notifications */}
            <ToastContainer
              position="top-right"
              autoClose={5000}
              hideProgressBar={false}
              newestOnTop={false}
              closeOnClick
              rtl={false}
              pauseOnFocusLoss
              draggable
              pauseOnHover
              theme="light"
            />
          </div>
        </Router>
      </WorkflowProvider>
    </AuthProvider>
  );
}

export default App;
